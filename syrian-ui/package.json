{"name": "syrian-identity-ui", "private": true, "description": "Syrian Identity UI - A comprehensive design system celebrating Syrian culture with RTL-first components", "version": "0.1.0", "author": "Syrian Identity UI Team", "license": "MIT", "keywords": ["design-system", "syrian", "rtl", "arabic", "react", "components", "cultural-design"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "clean": "turbo run clean && rm -rf node_modules", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo run build --filter=docs^... && changeset publish", "preview-storybook": "turbo run preview-storybook", "test": "turbo run test", "type-check": "turbo run type-check"}, "devDependencies": {"@changesets/cli": "^2.27.1", "prettier": "^3.2.5", "turbo": "^2.5.0"}, "packageManager": "pnpm@8.15.6", "repository": {"type": "git", "url": "https://github.com/syrian-identity/ui.git"}, "homepage": "https://syrian-identity-ui.com", "bugs": {"url": "https://github.com/syrian-identity/ui/issues"}}