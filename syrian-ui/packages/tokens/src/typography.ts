/**
 * Syrian Identity Typography System
 * 
 * Designed for Arabic-first experiences with proper fallbacks:
 * - Qomra: Premium Arabic typeface (requires license)
 * - Noto <PERSON> Arabic: Google's high-quality Arabic font
 * - Amiri: Traditional Arabic serif font
 * - System fallbacks for optimal performance
 * 
 * Features:
 * - RTL/LTR support
 * - Arabic ligature preservation
 * - Optimal line-height for Arabic text
 * - Cultural authenticity
 */

// Font Stacks
export const sidFonts = {
  // Arabic-optimized stack (primary)
  arabic: {
    primary: `'Qomra', 'Noto Kufi Arabic', 'Amiri', 'Tahoma', 'Arial Unicode MS', system-ui, sans-serif`,
    serif: `'<PERSON><PERSON>', 'Noto Naskh Arabic', 'Times New Roman', serif`,
    mono: `'Noto Sans Mono', 'SF Mono', 'Monaco', 'Cascadia Code', monospace`
  },
  
  // Latin-optimized stack (secondary)
  latin: {
    primary: `'Inter', 'SF Pro Display', system-ui, -apple-system, 'Segoe UI', 'Roboto', sans-serif`,
    serif: `'Charter', 'Georgia', 'Times New Roman', serif`,
    mono: `'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace`
  },

  // Universal stack (works for both)
  universal: `'Inter', 'Noto Kufi Arabic', system-ui, -apple-system, 'Segoe UI', 'Tahoma', sans-serif`
};

// Font Weights
export const sidFontWeights = {
  light: 300,
  regular: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
  extrabold: 800
} as const;

// Font Sizes (rem-based for accessibility)
export const sidFontSizes = {
  xs: '0.75rem',    // 12px
  sm: '0.875rem',   // 14px
  base: '1rem',     // 16px
  lg: '1.125rem',   // 18px
  xl: '1.25rem',    // 20px
  '2xl': '1.5rem',  // 24px
  '3xl': '1.875rem', // 30px
  '4xl': '2.25rem',  // 36px
  '5xl': '3rem',     // 48px
  '6xl': '3.75rem',  // 60px
  '7xl': '4.5rem',   // 72px
  '8xl': '6rem',     // 96px
  '9xl': '8rem'      // 128px
} as const;

// Line Heights (optimized for Arabic text)
export const sidLineHeights = {
  none: 1,
  tight: 1.25,
  snug: 1.375,
  normal: 1.5,      // Good for Arabic
  relaxed: 1.625,   // Better for Arabic paragraphs
  loose: 2,
  
  // Semantic line heights
  heading: 1.2,
  body: 1.6,        // Optimal for Arabic reading
  caption: 1.4
} as const;

// Letter Spacing (use sparingly with Arabic)
export const sidLetterSpacing = {
  tighter: '-0.05em',
  tight: '-0.025em', 
  normal: '0em',
  wide: '0.025em',
  wider: '0.05em',
  widest: '0.1em'
} as const;

// Typography Scale (semantic)
export const sidTypography = {
  // Display styles
  display: {
    '2xl': {
      fontSize: sidFontSizes['7xl'],
      lineHeight: sidLineHeights.none,
      fontWeight: sidFontWeights.bold,
      letterSpacing: sidLetterSpacing.tight
    },
    xl: {
      fontSize: sidFontSizes['6xl'],
      lineHeight: sidLineHeights.none,
      fontWeight: sidFontWeights.bold,
      letterSpacing: sidLetterSpacing.tight
    },
    lg: {
      fontSize: sidFontSizes['5xl'],
      lineHeight: sidLineHeights.none,
      fontWeight: sidFontWeights.bold,
      letterSpacing: sidLetterSpacing.tight
    },
    md: {
      fontSize: sidFontSizes['4xl'],
      lineHeight: sidLineHeights.tight,
      fontWeight: sidFontWeights.bold,
      letterSpacing: sidLetterSpacing.tight
    },
    sm: {
      fontSize: sidFontSizes['3xl'],
      lineHeight: sidLineHeights.tight,
      fontWeight: sidFontWeights.bold,
      letterSpacing: sidLetterSpacing.normal
    }
  },

  // Heading styles
  heading: {
    h1: {
      fontSize: sidFontSizes['2xl'],
      lineHeight: sidLineHeights.heading,
      fontWeight: sidFontWeights.bold,
      letterSpacing: sidLetterSpacing.normal
    },
    h2: {
      fontSize: sidFontSizes.xl,
      lineHeight: sidLineHeights.heading,
      fontWeight: sidFontWeights.semibold,
      letterSpacing: sidLetterSpacing.normal
    },
    h3: {
      fontSize: sidFontSizes.lg,
      lineHeight: sidLineHeights.heading,
      fontWeight: sidFontWeights.semibold,
      letterSpacing: sidLetterSpacing.normal
    },
    h4: {
      fontSize: sidFontSizes.base,
      lineHeight: sidLineHeights.heading,
      fontWeight: sidFontWeights.semibold,
      letterSpacing: sidLetterSpacing.normal
    },
    h5: {
      fontSize: sidFontSizes.sm,
      lineHeight: sidLineHeights.heading,
      fontWeight: sidFontWeights.semibold,
      letterSpacing: sidLetterSpacing.normal
    },
    h6: {
      fontSize: sidFontSizes.xs,
      lineHeight: sidLineHeights.heading,
      fontWeight: sidFontWeights.semibold,
      letterSpacing: sidLetterSpacing.wide
    }
  },

  // Body text styles
  body: {
    lg: {
      fontSize: sidFontSizes.lg,
      lineHeight: sidLineHeights.body,
      fontWeight: sidFontWeights.regular,
      letterSpacing: sidLetterSpacing.normal
    },
    md: {
      fontSize: sidFontSizes.base,
      lineHeight: sidLineHeights.body,
      fontWeight: sidFontWeights.regular,
      letterSpacing: sidLetterSpacing.normal
    },
    sm: {
      fontSize: sidFontSizes.sm,
      lineHeight: sidLineHeights.body,
      fontWeight: sidFontWeights.regular,
      letterSpacing: sidLetterSpacing.normal
    },
    xs: {
      fontSize: sidFontSizes.xs,
      lineHeight: sidLineHeights.normal,
      fontWeight: sidFontWeights.regular,
      letterSpacing: sidLetterSpacing.normal
    }
  },

  // Label styles
  label: {
    lg: {
      fontSize: sidFontSizes.sm,
      lineHeight: sidLineHeights.normal,
      fontWeight: sidFontWeights.medium,
      letterSpacing: sidLetterSpacing.normal
    },
    md: {
      fontSize: sidFontSizes.xs,
      lineHeight: sidLineHeights.normal,
      fontWeight: sidFontWeights.medium,
      letterSpacing: sidLetterSpacing.normal
    },
    sm: {
      fontSize: sidFontSizes.xs,
      lineHeight: sidLineHeights.normal,
      fontWeight: sidFontWeights.medium,
      letterSpacing: sidLetterSpacing.wide
    }
  }
};

// CSS Font Loading (for Qomra and other custom fonts)
export const sidFontFaces = `
  @font-face {
    font-family: 'Qomra';
    src: url('/fonts/qomra-regular.woff2') format('woff2'),
         url('/fonts/qomra-regular.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
    unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF, U+FB50-FDFF, U+FE70-FEFF;
  }

  @font-face {
    font-family: 'Qomra';
    src: url('/fonts/qomra-bold.woff2') format('woff2'),
         url('/fonts/qomra-bold.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
    unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF, U+FB50-FDFF, U+FE70-FEFF;
  }
`;
