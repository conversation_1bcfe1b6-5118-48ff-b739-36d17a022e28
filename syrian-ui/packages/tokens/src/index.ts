/**
 * Syrian Identity Design Tokens
 * 
 * A comprehensive design token system for Syrian cultural identity,
 * featuring RTL-first design, Arabic typography, and cultural color palettes.
 * 
 * @package @sid/tokens
 * @version 0.1.0
 * <AUTHOR> Identity UI Team
 */

// Colors
export * from './colors';
export {
  sidColors,
  forestColors,
  wheatColors, 
  umberColors,
  charcoalColors,
  flagColors,
  semanticColors,
  getColorValue
} from './colors';

// Typography
export * from './typography';
export {
  sidFonts,
  sidFontWeights,
  sidFontSizes,
  sidLineHeights,
  sidLetterSpacing,
  sidTypography,
  sidFontFaces
} from './typography';

// Spacing & Layout
export * from './spacing';
export {
  sidSpace,
  sidSpacing,
  sidRadius,
  sidShadows,
  sidZIndex,
  sidBreakpoints,
  sidContainers,
  sidGrid,
  sidAspectRatios
} from './spacing';

// Animations
export * from './animations';
export {
  sidDurations,
  sidEasing,
  sidDelays,
  sidSprings,
  sidKeyframes,
  sidAnimations,
  sidAnimationTokens,
  animationUtils
} from './animations';

// CSS Variables
export * from './css-vars';
export {
  sidCssVars,
  tokensCss,
  injectSidTokens
} from './css-vars';

// Token Categories for programmatic access
export const tokenCategories = {
  colors: [
    'flag', 'forest', 'wheat', 'umber', 'charcoal',
    'semantic', 'text', 'background', 'border'
  ],
  typography: [
    'fonts', 'fontWeights', 'fontSizes', 'lineHeights',
    'letterSpacing', 'typography'
  ],
  spacing: [
    'space', 'spacing', 'radius', 'shadows', 'zIndex',
    'breakpoints', 'containers', 'grid', 'aspectRatios'
  ],
  animations: [
    'durations', 'easing', 'delays', 'springs',
    'keyframes', 'animations', 'utils'
  ]
} as const;

// Version info
export const version = '0.1.0';
export const name = '@sid/tokens';

// Cultural metadata
export const culturalInfo = {
  inspiration: 'Syrian visual identity and cultural heritage',
  colorSources: [
    'Syrian flag (Red: #CE1126, Green: #007A3D)',
    'Syrian.zone cultural palette',
    'Damascus architectural colors',
    'Traditional Syrian textiles'
  ],
  typographySources: [
    'Qomra typeface (premium Arabic font)',
    'Traditional Arabic calligraphy',
    'Syrian manuscript traditions'
  ],
  designPrinciples: [
    'RTL-first approach',
    'Cultural authenticity',
    'Accessibility (WCAG AA)',
    'Islamic geometric harmony',
    'Syrian flag proportions (2:3 ratio)'
  ]
} as const;
