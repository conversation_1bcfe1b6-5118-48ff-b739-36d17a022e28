/**
 * Syrian Identity Animation Tokens
 * 
 * Animation system inspired by Syrian cultural rhythm and Islamic art principles:
 * - Timing based on traditional Syrian music rhythms
 * - Easing curves inspired by Arabic calligraphy flow
 * - Durations reflecting cultural pacing and contemplation
 * - Geometric animation patterns from Islamic art
 * 
 * Sources:
 * - Syrian traditional music timing (Maqam rhythms)
 * - Islamic geometric pattern mathematics
 * - Arabic calligraphy stroke dynamics
 * - Damascus architectural proportions
 */

// Animation Durations (in milliseconds)
// Based on Syrian musical rhythms and cultural pacing
export const sidDurations = {
  // Micro-interactions (quick feedback)
  instant: 0,
  immediate: 50,   // Button press feedback
  quick: 100,      // Hover states
  fast: 150,       // Focus transitions
  
  // Standard interactions (comfortable pacing)
  normal: 250,     // Default transition
  moderate: 350,   // Form field changes
  comfortable: 500, // Modal appearances
  
  // Contemplative timing (cultural pacing)
  slow: 750,       // Page transitions
  deliberate: 1000, // Loading states
  contemplative: 1500, // Storytelling animations
  ceremonial: 2000,    // Cultural presentations
  
  // Extended animations (for emphasis)
  extended: 3000,  // Hero animations
  epic: 5000,      // Full-screen transitions
} as const;

// Animation Easing Functions
// Inspired by Arabic calligraphy and Islamic geometric principles
export const sidEasing = {
  // Linear (geometric precision)
  linear: 'linear',
  
  // Standard easing (comfortable flow)
  ease: 'ease',
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',
  
  // Calligraphy-inspired curves (flowing, organic)
  calligraphy: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',      // Smooth ink flow
  flourish: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',       // Decorative curves
  graceful: 'cubic-bezier(0.23, 1, 0.32, 1)',               // Elegant movement
  
  // Geometric precision (Islamic art patterns)
  geometric: 'cubic-bezier(0.4, 0, 0.6, 1)',                // Precise, mathematical
  crystalline: 'cubic-bezier(0.25, 0, 0.75, 1)',            // Sharp, clean
  architectural: 'cubic-bezier(0.645, 0.045, 0.355, 1)',    // Structural, solid
  
  // Cultural rhythm (Syrian musical timing)
  rhythmic: 'cubic-bezier(0.4, 0, 0.2, 1)',                 // Steady beat
  syncopated: 'cubic-bezier(0.68, -0.6, 0.32, 1.6)',        // Off-beat emphasis
  melodic: 'cubic-bezier(0.25, 0.1, 0.25, 1)',              // Musical flow
  
  // Emotional expressions
  joyful: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',         // Bouncy, celebratory
  serene: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',           // Peaceful, calm
  dramatic: 'cubic-bezier(0.95, 0.05, 0.795, 0.035)',       // Bold, impactful
  contemplative: 'cubic-bezier(0.23, 1, 0.32, 1)',          // Thoughtful, slow
} as const;

// Animation Delays
// For staggered animations and orchestrated sequences
export const sidDelays = {
  none: 0,
  micro: 25,       // Tiny stagger
  small: 50,       // Button sequences
  medium: 100,     // List item reveals
  large: 200,      // Card animations
  xlarge: 300,     // Section transitions
  xxlarge: 500,    // Page-level sequences
} as const;

// Spring Animation Configurations
// For physics-based animations with cultural character
export const sidSprings = {
  // Gentle springs (contemplative, peaceful)
  gentle: {
    tension: 120,
    friction: 14,
    mass: 1,
  },
  
  // Moderate springs (standard interactions)
  moderate: {
    tension: 170,
    friction: 26,
    mass: 1,
  },
  
  // Energetic springs (joyful, celebratory)
  energetic: {
    tension: 300,
    friction: 30,
    mass: 1,
  },
  
  // Dramatic springs (bold, impactful)
  dramatic: {
    tension: 400,
    friction: 40,
    mass: 1,
  },
  
  // Architectural springs (solid, structural)
  architectural: {
    tension: 200,
    friction: 35,
    mass: 1.2,
  },
} as const;

// Keyframe Animations
// Pre-defined animations for common patterns
export const sidKeyframes = {
  // Fade animations
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  
  fadeOut: {
    from: { opacity: 1 },
    to: { opacity: 0 },
  },
  
  // Scale animations (geometric growth)
  scaleIn: {
    from: { transform: 'scale(0.8)', opacity: 0 },
    to: { transform: 'scale(1)', opacity: 1 },
  },
  
  scaleOut: {
    from: { transform: 'scale(1)', opacity: 1 },
    to: { transform: 'scale(0.8)', opacity: 0 },
  },
  
  // Slide animations (RTL-aware)
  slideInRight: {
    from: { transform: 'translateX(100%)', opacity: 0 },
    to: { transform: 'translateX(0)', opacity: 1 },
  },
  
  slideInLeft: {
    from: { transform: 'translateX(-100%)', opacity: 0 },
    to: { transform: 'translateX(0)', opacity: 1 },
  },
  
  slideInUp: {
    from: { transform: 'translateY(100%)', opacity: 0 },
    to: { transform: 'translateY(0)', opacity: 1 },
  },
  
  slideInDown: {
    from: { transform: 'translateY(-100%)', opacity: 0 },
    to: { transform: 'translateY(0)', opacity: 1 },
  },
  
  // Rotation animations (geometric patterns)
  rotate360: {
    from: { transform: 'rotate(0deg)' },
    to: { transform: 'rotate(360deg)' },
  },
  
  // Pulse animation (heartbeat, life)
  pulse: {
    '0%': { transform: 'scale(1)', opacity: 1 },
    '50%': { transform: 'scale(1.05)', opacity: 0.8 },
    '100%': { transform: 'scale(1)', opacity: 1 },
  },
  
  // Bounce animation (joyful)
  bounce: {
    '0%, 20%, 53%, 80%, 100%': { transform: 'translateY(0)' },
    '40%, 43%': { transform: 'translateY(-8px)' },
    '70%': { transform: 'translateY(-4px)' },
    '90%': { transform: 'translateY(-2px)' },
  },
  
  // Shimmer animation (loading states)
  shimmer: {
    '0%': { backgroundPosition: '-200px 0' },
    '100%': { backgroundPosition: 'calc(200px + 100%) 0' },
  },
  
  // Geometric pattern animation
  geometricGrow: {
    '0%': { 
      transform: 'scale(0) rotate(0deg)',
      opacity: 0,
    },
    '50%': {
      transform: 'scale(1.1) rotate(180deg)',
      opacity: 0.8,
    },
    '100%': {
      transform: 'scale(1) rotate(360deg)',
      opacity: 1,
    },
  },
} as const;

// Animation Presets
// Complete animation configurations for common use cases
export const sidAnimations = {
  // Button interactions
  buttonHover: {
    duration: sidDurations.fast,
    easing: sidEasing.calligraphy,
    properties: ['background-color', 'border-color', 'color', 'transform'],
  },
  
  buttonPress: {
    duration: sidDurations.immediate,
    easing: sidEasing.geometric,
    properties: ['transform'],
  },
  
  // Form interactions
  inputFocus: {
    duration: sidDurations.normal,
    easing: sidEasing.graceful,
    properties: ['border-color', 'box-shadow'],
  },
  
  // Modal animations
  modalEnter: {
    duration: sidDurations.comfortable,
    easing: sidEasing.architectural,
    keyframes: sidKeyframes.scaleIn,
  },
  
  modalExit: {
    duration: sidDurations.normal,
    easing: sidEasing.geometric,
    keyframes: sidKeyframes.scaleOut,
  },
  
  // Loading animations
  spinner: {
    duration: sidDurations.deliberate,
    easing: sidEasing.linear,
    keyframes: sidKeyframes.rotate360,
    iterationCount: 'infinite',
  },
  
  // Page transitions
  pageEnter: {
    duration: sidDurations.slow,
    easing: sidEasing.melodic,
    keyframes: sidKeyframes.slideInRight,
  },
  
  // Cultural animations
  damascusPattern: {
    duration: sidDurations.ceremonial,
    easing: sidEasing.contemplative,
    keyframes: sidKeyframes.geometricGrow,
  },
  
  arabicText: {
    duration: sidDurations.moderate,
    easing: sidEasing.calligraphy,
    properties: ['opacity', 'transform'],
  },
} as const;

// Utility functions for animation
export const animationUtils = {
  /**
   * Create a CSS transition string
   */
  createTransition: (
    properties: string[],
    duration: keyof typeof sidDurations = 'normal',
    easing: keyof typeof sidEasing = 'ease'
  ): string => {
    return properties
      .map(prop => `${prop} ${sidDurations[duration]}ms ${sidEasing[easing]}`)
      .join(', ');
  },
  
  /**
   * Create staggered delay for list animations
   */
  createStaggerDelay: (
    index: number,
    baseDelay: keyof typeof sidDelays = 'medium'
  ): number => {
    return sidDelays[baseDelay] * index;
  },
  
  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion: (): boolean => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },
  
  /**
   * Get duration with reduced motion consideration
   */
  getDuration: (
    duration: keyof typeof sidDurations,
    respectReducedMotion: boolean = true
  ): number => {
    if (respectReducedMotion && animationUtils.prefersReducedMotion()) {
      return sidDurations.instant;
    }
    return sidDurations[duration];
  },
};

// Export all animation tokens
export const sidAnimationTokens = {
  durations: sidDurations,
  easing: sidEasing,
  delays: sidDelays,
  springs: sidSprings,
  keyframes: sidKeyframes,
  animations: sidAnimations,
  utils: animationUtils,
} as const;
