/**
 * Syrian Identity Color Palette
 * 
 * Based on the official Syrian visual identity and cultural heritage:
 * - Forest Green: From Syrian landscapes and olive groves
 * - Golden Wheat: From Syrian agricultural heritage  
 * - Deep Umber: From Damascus architecture and earth
 * - Charcoal: From traditional calligraphy and stone
 * 
 * Sources: 
 * - Syrian flag colors (Red: #CE1126, White: #FFFFFF, Black: #000000, Green: #007A3D)
 * - Syrian.zone cultural palette
 * - Traditional Damascus architectural colors
 */

export const sidColors = {
  // Syrian Flag Colors (Official)
  flag: {
    red: '#CE1126',
    white: '#FFFFFF', 
    black: '#000000',
    green: '#007A3D'
  },

  // Cultural Heritage Palette
  forest: {
    50: '#F0F9F7',
    100: '#D1F2E8',
    200: '#A3E5D1',
    300: '#6DD4B6',
    400: '#42BF9A',
    500: '#428177', // Primary forest green
    600: '#357A6B',
    700: '#054239', // Deep forest
    800: '#043530',
    900: '#002623', // Darkest forest
    950: '#001A18'
  },

  wheat: {
    50: '#FEFCF8',
    100: '#EDEBE0', // Light wheat
    200: '#E2DDD0',
    300: '#D4CDB8',
    400: '#B9A779', // Golden wheat
    500: '#A69660',
    600: '#988561', // Rich wheat
    700: '#7A6B4D',
    800: '#5C5139',
    900: '#3E3626',
    950: '#2A2419'
  },

  umber: {
    50: '#FDF7F7',
    100: '#F5E8E9',
    200: '#EBD1D4',
    300: '#DDB5BA',
    400: '#C8919A',
    500: '#6B1F2A', // Deep umber
    600: '#5A1A23',
    700: '#4A151E', // Rich umber
    800: '#3A111A',
    900: '#260F14', // Dark umber
    950: '#1A0B0E'
  },

  charcoal: {
    0: '#FFFFFF',    // Pure white
    50: '#F8F8F8',
    100: '#F0F0F0',
    200: '#E4E4E4',
    300: '#D1D1D1',
    400: '#B4B4B4',
    500: '#9A9A9A',
    600: '#3D3A3B',   // Medium charcoal
    700: '#2D2A2B',
    800: '#1F1C1D',
    900: '#161616',   // Deep charcoal
    950: '#0A0A0A'
  },

  // Semantic Colors
  semantic: {
    success: '#007A3D',  // Syrian flag green
    warning: '#B9A779',  // Golden wheat
    error: '#CE1126',    // Syrian flag red
    info: '#428177',     // Forest green
  },

  // Accessibility Colors (WCAG AA compliant)
  text: {
    primary: '#161616',     // charcoal-900
    secondary: '#3D3A3B',   // charcoal-600
    tertiary: '#9A9A9A',    // charcoal-500
    inverse: '#FFFFFF',     // charcoal-0
    onColor: '#FFFFFF',     // For colored backgrounds
  },

  background: {
    primary: '#FFFFFF',     // charcoal-0
    secondary: '#F8F8F8',   // charcoal-50
    tertiary: '#F0F0F0',    // charcoal-100
    inverse: '#161616',     // charcoal-900
  },

  border: {
    primary: '#E4E4E4',     // charcoal-200
    secondary: '#D1D1D1',   // charcoal-300
    focus: '#428177',       // forest-500
    error: '#CE1126',       // flag-red
  }
};

// Color utilities for programmatic access
export const getColorValue = (colorPath: string): string => {
  const keys = colorPath.split('.');
  let value: any = sidColors;
  
  for (const key of keys) {
    value = value?.[key];
  }
  
  return typeof value === 'string' ? value : '';
};

// Export individual color scales for convenience
export const forestColors = sidColors.forest;
export const wheatColors = sidColors.wheat;
export const umberColors = sidColors.umber;
export const charcoalColors = sidColors.charcoal;
export const flagColors = sidColors.flag;
export const semanticColors = sidColors.semantic;
