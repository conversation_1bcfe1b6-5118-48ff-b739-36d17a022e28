/**
 * Syrian Identity Spacing & Layout System
 * 
 * Based on a 4px base unit with cultural proportions:
 * - Golden ratio influences (1.618)
 * - Syrian flag proportions (2:3 ratio)
 * - Islamic geometric harmony
 * - Accessibility-friendly touch targets (44px minimum)
 */

// Base spacing unit (4px)
const BASE_UNIT = 4;

// Spacing Scale (4px base)
export const sidSpace = {
  0: '0',
  px: '1px',
  0.5: `${BASE_UNIT * 0.125}px`, // 0.5px
  1: `${BASE_UNIT * 0.25}px`,    // 1px
  1.5: `${BASE_UNIT * 0.375}px`, // 1.5px
  2: `${BASE_UNIT * 0.5}px`,     // 2px
  2.5: `${BASE_UNIT * 0.625}px`, // 2.5px
  3: `${BASE_UNIT * 0.75}px`,    // 3px
  3.5: `${BASE_UNIT * 0.875}px`, // 3.5px
  4: `${BASE_UNIT}px`,           // 4px
  5: `${BASE_UNIT * 1.25}px`,    // 5px
  6: `${BASE_UNIT * 1.5}px`,     // 6px
  7: `${BASE_UNIT * 1.75}px`,    // 7px
  8: `${BASE_UNIT * 2}px`,       // 8px
  9: `${BASE_UNIT * 2.25}px`,    // 9px
  10: `${BASE_UNIT * 2.5}px`,    // 10px
  11: `${BASE_UNIT * 2.75}px`,   // 11px
  12: `${BASE_UNIT * 3}px`,      // 12px
  14: `${BASE_UNIT * 3.5}px`,    // 14px
  16: `${BASE_UNIT * 4}px`,      // 16px
  20: `${BASE_UNIT * 5}px`,      // 20px
  24: `${BASE_UNIT * 6}px`,      // 24px
  28: `${BASE_UNIT * 7}px`,      // 28px
  32: `${BASE_UNIT * 8}px`,      // 32px
  36: `${BASE_UNIT * 9}px`,      // 36px
  40: `${BASE_UNIT * 10}px`,     // 40px
  44: `${BASE_UNIT * 11}px`,     // 44px (min touch target)
  48: `${BASE_UNIT * 12}px`,     // 48px
  52: `${BASE_UNIT * 13}px`,     // 52px
  56: `${BASE_UNIT * 14}px`,     // 56px
  60: `${BASE_UNIT * 15}px`,     // 60px
  64: `${BASE_UNIT * 16}px`,     // 64px
  72: `${BASE_UNIT * 18}px`,     // 72px
  80: `${BASE_UNIT * 20}px`,     // 80px
  96: `${BASE_UNIT * 24}px`,     // 96px
} as const;

// Semantic Spacing
export const sidSpacing = {
  // Component spacing
  component: {
    xs: sidSpace[4],    // 4px
    sm: sidSpace[8],    // 8px
    md: sidSpace[12],   // 12px
    lg: sidSpace[16],   // 16px
    xl: sidSpace[20],   // 20px
    '2xl': sidSpace[24], // 24px
    '3xl': sidSpace[32], // 32px
    '4xl': sidSpace[40], // 40px
    '5xl': sidSpace[48], // 48px
    '6xl': sidSpace[64], // 64px
  },

  // Layout spacing
  layout: {
    xs: sidSpace[16],   // 16px
    sm: sidSpace[24],   // 24px
    md: sidSpace[32],   // 32px
    lg: sidSpace[48],   // 48px
    xl: sidSpace[64],   // 64px
    '2xl': sidSpace[80], // 80px
    '3xl': sidSpace[96], // 96px
  },

  // Container spacing
  container: {
    xs: sidSpace[16],   // 16px
    sm: sidSpace[20],   // 20px
    md: sidSpace[24],   // 24px
    lg: sidSpace[32],   // 32px
    xl: sidSpace[40],   // 40px
  }
};

// Border Radius (inspired by Islamic geometric patterns)
export const sidRadius = {
  none: '0',
  sm: '2px',
  md: '4px',
  lg: '6px',
  xl: '8px',
  '2xl': '12px',
  '3xl': '16px',
  full: '9999px',
  
  // Semantic radius
  button: '6px',
  input: '4px',
  card: '8px',
  modal: '12px',
  avatar: '9999px'
} as const;

// Shadows (subtle, culturally appropriate)
export const sidShadows = {
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  lg: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  xl: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  '2xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  
  // Semantic shadows
  button: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  card: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  modal: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  focus: '0 0 0 3px rgba(66, 129, 119, 0.1)', // forest-500 with opacity
} as const;

// Z-Index Scale
export const sidZIndex = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800
} as const;

// Breakpoints (mobile-first, RTL-aware)
export const sidBreakpoints = {
  xs: '320px',   // Small phones
  sm: '640px',   // Large phones
  md: '768px',   // Tablets
  lg: '1024px',  // Small laptops
  xl: '1280px',  // Large laptops
  '2xl': '1536px' // Desktops
} as const;

// Container Max Widths
export const sidContainers = {
  xs: '100%',
  sm: '640px',
  md: '768px', 
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
} as const;

// Grid System (12-column)
export const sidGrid = {
  columns: 12,
  gap: {
    xs: sidSpace[12], // 12px
    sm: sidSpace[16], // 16px
    md: sidSpace[20], // 20px
    lg: sidSpace[24], // 24px
    xl: sidSpace[32], // 32px
  }
} as const;

// Aspect Ratios (including Syrian flag ratio)
export const sidAspectRatios = {
  square: '1 / 1',
  video: '16 / 9',
  photo: '4 / 3',
  portrait: '3 / 4',
  flag: '2 / 3',      // Syrian flag ratio
  golden: '1.618 / 1', // Golden ratio
  wide: '21 / 9'
} as const;
