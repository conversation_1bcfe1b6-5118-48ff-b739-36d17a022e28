/**
 * Syrian Identity CSS Variables
 * 
 * Converts design tokens to CSS custom properties for use in stylesheets.
 * Includes RTL-aware logical properties and cultural color mappings.
 */

import { sidColors } from './colors';
import { sidFonts, sidFontSizes, sidFontWeights, sidLineHeights } from './typography';
import { sidSpace, sidRadius, sidShadows, sidZIndex } from './spacing';
import { sidDurations, sidEasing, sidDelays } from './animations';

// Generate CSS variables string
export const sidCssVars = `
:root {
  /* === COLORS === */
  
  /* Flag Colors */
  --sid-flag-red: ${sidColors.flag.red};
  --sid-flag-white: ${sidColors.flag.white};
  --sid-flag-black: ${sidColors.flag.black};
  --sid-flag-green: ${sidColors.flag.green};

  /* Forest Scale */
  --sid-forest-50: ${sidColors.forest[50]};
  --sid-forest-100: ${sidColors.forest[100]};
  --sid-forest-200: ${sidColors.forest[200]};
  --sid-forest-300: ${sidColors.forest[300]};
  --sid-forest-400: ${sidColors.forest[400]};
  --sid-forest-500: ${sidColors.forest[500]};
  --sid-forest-600: ${sidColors.forest[600]};
  --sid-forest-700: ${sidColors.forest[700]};
  --sid-forest-800: ${sidColors.forest[800]};
  --sid-forest-900: ${sidColors.forest[900]};
  --sid-forest-950: ${sidColors.forest[950]};

  /* Wheat Scale */
  --sid-wheat-50: ${sidColors.wheat[50]};
  --sid-wheat-100: ${sidColors.wheat[100]};
  --sid-wheat-200: ${sidColors.wheat[200]};
  --sid-wheat-300: ${sidColors.wheat[300]};
  --sid-wheat-400: ${sidColors.wheat[400]};
  --sid-wheat-500: ${sidColors.wheat[500]};
  --sid-wheat-600: ${sidColors.wheat[600]};
  --sid-wheat-700: ${sidColors.wheat[700]};
  --sid-wheat-800: ${sidColors.wheat[800]};
  --sid-wheat-900: ${sidColors.wheat[900]};
  --sid-wheat-950: ${sidColors.wheat[950]};

  /* Umber Scale */
  --sid-umber-50: ${sidColors.umber[50]};
  --sid-umber-100: ${sidColors.umber[100]};
  --sid-umber-200: ${sidColors.umber[200]};
  --sid-umber-300: ${sidColors.umber[300]};
  --sid-umber-400: ${sidColors.umber[400]};
  --sid-umber-500: ${sidColors.umber[500]};
  --sid-umber-600: ${sidColors.umber[600]};
  --sid-umber-700: ${sidColors.umber[700]};
  --sid-umber-800: ${sidColors.umber[800]};
  --sid-umber-900: ${sidColors.umber[900]};
  --sid-umber-950: ${sidColors.umber[950]};

  /* Charcoal Scale */
  --sid-charcoal-0: ${sidColors.charcoal[0]};
  --sid-charcoal-50: ${sidColors.charcoal[50]};
  --sid-charcoal-100: ${sidColors.charcoal[100]};
  --sid-charcoal-200: ${sidColors.charcoal[200]};
  --sid-charcoal-300: ${sidColors.charcoal[300]};
  --sid-charcoal-400: ${sidColors.charcoal[400]};
  --sid-charcoal-500: ${sidColors.charcoal[500]};
  --sid-charcoal-600: ${sidColors.charcoal[600]};
  --sid-charcoal-700: ${sidColors.charcoal[700]};
  --sid-charcoal-800: ${sidColors.charcoal[800]};
  --sid-charcoal-900: ${sidColors.charcoal[900]};
  --sid-charcoal-950: ${sidColors.charcoal[950]};

  /* Semantic Colors */
  --sid-success: ${sidColors.semantic.success};
  --sid-warning: ${sidColors.semantic.warning};
  --sid-error: ${sidColors.semantic.error};
  --sid-info: ${sidColors.semantic.info};

  /* Text Colors */
  --sid-text-primary: ${sidColors.text.primary};
  --sid-text-secondary: ${sidColors.text.secondary};
  --sid-text-tertiary: ${sidColors.text.tertiary};
  --sid-text-inverse: ${sidColors.text.inverse};
  --sid-text-on-color: ${sidColors.text.onColor};

  /* Background Colors */
  --sid-bg-primary: ${sidColors.background.primary};
  --sid-bg-secondary: ${sidColors.background.secondary};
  --sid-bg-tertiary: ${sidColors.background.tertiary};
  --sid-bg-inverse: ${sidColors.background.inverse};

  /* Border Colors */
  --sid-border-primary: ${sidColors.border.primary};
  --sid-border-secondary: ${sidColors.border.secondary};
  --sid-border-focus: ${sidColors.border.focus};
  --sid-border-error: ${sidColors.border.error};

  /* === TYPOGRAPHY === */
  
  /* Font Families */
  --sid-font-arabic: ${sidFonts.arabic.primary};
  --sid-font-latin: ${sidFonts.latin.primary};
  --sid-font-universal: ${sidFonts.universal};
  --sid-font-mono: ${sidFonts.arabic.mono};

  /* Font Sizes */
  --sid-text-xs: ${sidFontSizes.xs};
  --sid-text-sm: ${sidFontSizes.sm};
  --sid-text-base: ${sidFontSizes.base};
  --sid-text-lg: ${sidFontSizes.lg};
  --sid-text-xl: ${sidFontSizes.xl};
  --sid-text-2xl: ${sidFontSizes['2xl']};
  --sid-text-3xl: ${sidFontSizes['3xl']};
  --sid-text-4xl: ${sidFontSizes['4xl']};
  --sid-text-5xl: ${sidFontSizes['5xl']};
  --sid-text-6xl: ${sidFontSizes['6xl']};
  --sid-text-7xl: ${sidFontSizes['7xl']};
  --sid-text-8xl: ${sidFontSizes['8xl']};
  --sid-text-9xl: ${sidFontSizes['9xl']};

  /* Font Weights */
  --sid-font-light: ${sidFontWeights.light};
  --sid-font-regular: ${sidFontWeights.regular};
  --sid-font-medium: ${sidFontWeights.medium};
  --sid-font-semibold: ${sidFontWeights.semibold};
  --sid-font-bold: ${sidFontWeights.bold};
  --sid-font-extrabold: ${sidFontWeights.extrabold};

  /* Line Heights */
  --sid-leading-none: ${sidLineHeights.none};
  --sid-leading-tight: ${sidLineHeights.tight};
  --sid-leading-snug: ${sidLineHeights.snug};
  --sid-leading-normal: ${sidLineHeights.normal};
  --sid-leading-relaxed: ${sidLineHeights.relaxed};
  --sid-leading-loose: ${sidLineHeights.loose};
  --sid-leading-heading: ${sidLineHeights.heading};
  --sid-leading-body: ${sidLineHeights.body};
  --sid-leading-caption: ${sidLineHeights.caption};

  /* === SPACING === */
  
  /* Space Scale */
  --sid-space-0: ${sidSpace[0]};
  --sid-space-px: ${sidSpace.px};
  --sid-space-0_5: ${sidSpace[0.5]};
  --sid-space-1: ${sidSpace[1]};
  --sid-space-1_5: ${sidSpace[1.5]};
  --sid-space-2: ${sidSpace[2]};
  --sid-space-2_5: ${sidSpace[2.5]};
  --sid-space-3: ${sidSpace[3]};
  --sid-space-3_5: ${sidSpace[3.5]};
  --sid-space-4: ${sidSpace[4]};
  --sid-space-5: ${sidSpace[5]};
  --sid-space-6: ${sidSpace[6]};
  --sid-space-7: ${sidSpace[7]};
  --sid-space-8: ${sidSpace[8]};
  --sid-space-9: ${sidSpace[9]};
  --sid-space-10: ${sidSpace[10]};
  --sid-space-11: ${sidSpace[11]};
  --sid-space-12: ${sidSpace[12]};
  --sid-space-14: ${sidSpace[14]};
  --sid-space-16: ${sidSpace[16]};
  --sid-space-20: ${sidSpace[20]};
  --sid-space-24: ${sidSpace[24]};
  --sid-space-28: ${sidSpace[28]};
  --sid-space-32: ${sidSpace[32]};
  --sid-space-36: ${sidSpace[36]};
  --sid-space-40: ${sidSpace[40]};
  --sid-space-44: ${sidSpace[44]};
  --sid-space-48: ${sidSpace[48]};
  --sid-space-52: ${sidSpace[52]};
  --sid-space-56: ${sidSpace[56]};
  --sid-space-60: ${sidSpace[60]};
  --sid-space-64: ${sidSpace[64]};
  --sid-space-72: ${sidSpace[72]};
  --sid-space-80: ${sidSpace[80]};
  --sid-space-96: ${sidSpace[96]};

  /* Border Radius */
  --sid-radius-none: ${sidRadius.none};
  --sid-radius-sm: ${sidRadius.sm};
  --sid-radius-md: ${sidRadius.md};
  --sid-radius-lg: ${sidRadius.lg};
  --sid-radius-xl: ${sidRadius.xl};
  --sid-radius-2xl: ${sidRadius['2xl']};
  --sid-radius-3xl: ${sidRadius['3xl']};
  --sid-radius-full: ${sidRadius.full};
  --sid-radius-button: ${sidRadius.button};
  --sid-radius-input: ${sidRadius.input};
  --sid-radius-card: ${sidRadius.card};
  --sid-radius-modal: ${sidRadius.modal};
  --sid-radius-avatar: ${sidRadius.avatar};

  /* Shadows */
  --sid-shadow-none: ${sidShadows.none};
  --sid-shadow-sm: ${sidShadows.sm};
  --sid-shadow-md: ${sidShadows.md};
  --sid-shadow-lg: ${sidShadows.lg};
  --sid-shadow-xl: ${sidShadows.xl};
  --sid-shadow-2xl: ${sidShadows['2xl']};
  --sid-shadow-inner: ${sidShadows.inner};
  --sid-shadow-button: ${sidShadows.button};
  --sid-shadow-card: ${sidShadows.card};
  --sid-shadow-modal: ${sidShadows.modal};
  --sid-shadow-focus: ${sidShadows.focus};

  /* Z-Index */
  --sid-z-hide: ${sidZIndex.hide};
  --sid-z-auto: ${sidZIndex.auto};
  --sid-z-base: ${sidZIndex.base};
  --sid-z-docked: ${sidZIndex.docked};
  --sid-z-dropdown: ${sidZIndex.dropdown};
  --sid-z-sticky: ${sidZIndex.sticky};
  --sid-z-banner: ${sidZIndex.banner};
  --sid-z-overlay: ${sidZIndex.overlay};
  --sid-z-modal: ${sidZIndex.modal};
  --sid-z-popover: ${sidZIndex.popover};
  --sid-z-skip-link: ${sidZIndex.skipLink};
  --sid-z-toast: ${sidZIndex.toast};
  --sid-z-tooltip: ${sidZIndex.tooltip};

  /* === ANIMATIONS === */

  /* Animation Durations */
  --sid-duration-instant: ${sidDurations.instant}ms;
  --sid-duration-immediate: ${sidDurations.immediate}ms;
  --sid-duration-quick: ${sidDurations.quick}ms;
  --sid-duration-fast: ${sidDurations.fast}ms;
  --sid-duration-normal: ${sidDurations.normal}ms;
  --sid-duration-moderate: ${sidDurations.moderate}ms;
  --sid-duration-comfortable: ${sidDurations.comfortable}ms;
  --sid-duration-slow: ${sidDurations.slow}ms;
  --sid-duration-deliberate: ${sidDurations.deliberate}ms;
  --sid-duration-contemplative: ${sidDurations.contemplative}ms;
  --sid-duration-ceremonial: ${sidDurations.ceremonial}ms;
  --sid-duration-extended: ${sidDurations.extended}ms;
  --sid-duration-epic: ${sidDurations.epic}ms;

  /* Animation Easing */
  --sid-ease-linear: ${sidEasing.linear};
  --sid-ease: ${sidEasing.ease};
  --sid-ease-in: ${sidEasing.easeIn};
  --sid-ease-out: ${sidEasing.easeOut};
  --sid-ease-in-out: ${sidEasing.easeInOut};
  --sid-ease-calligraphy: ${sidEasing.calligraphy};
  --sid-ease-flourish: ${sidEasing.flourish};
  --sid-ease-graceful: ${sidEasing.graceful};
  --sid-ease-geometric: ${sidEasing.geometric};
  --sid-ease-crystalline: ${sidEasing.crystalline};
  --sid-ease-architectural: ${sidEasing.architectural};
  --sid-ease-rhythmic: ${sidEasing.rhythmic};
  --sid-ease-syncopated: ${sidEasing.syncopated};
  --sid-ease-melodic: ${sidEasing.melodic};
  --sid-ease-joyful: ${sidEasing.joyful};
  --sid-ease-serene: ${sidEasing.serene};
  --sid-ease-dramatic: ${sidEasing.dramatic};
  --sid-ease-contemplative: ${sidEasing.contemplative};

  /* Animation Delays */
  --sid-delay-none: ${sidDelays.none}ms;
  --sid-delay-micro: ${sidDelays.micro}ms;
  --sid-delay-small: ${sidDelays.small}ms;
  --sid-delay-medium: ${sidDelays.medium}ms;
  --sid-delay-large: ${sidDelays.large}ms;
  --sid-delay-xlarge: ${sidDelays.xlarge}ms;
  --sid-delay-xxlarge: ${sidDelays.xxlarge}ms;
}

/* RTL-specific adjustments */
[dir="rtl"] {
  /* Any RTL-specific token overrides can go here */
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  :root {
    /* Dark mode color overrides will go here in Phase 2 */
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  :root {
    /* Override all animation durations to instant for accessibility */
    --sid-duration-immediate: 0ms;
    --sid-duration-quick: 0ms;
    --sid-duration-fast: 0ms;
    --sid-duration-normal: 0ms;
    --sid-duration-moderate: 0ms;
    --sid-duration-comfortable: 0ms;
    --sid-duration-slow: 0ms;
    --sid-duration-deliberate: 0ms;
    --sid-duration-contemplative: 0ms;
    --sid-duration-ceremonial: 0ms;
    --sid-duration-extended: 0ms;
    --sid-duration-epic: 0ms;

    /* Keep delays minimal but not zero for stagger effects */
    --sid-delay-micro: 0ms;
    --sid-delay-small: 0ms;
    --sid-delay-medium: 0ms;
    --sid-delay-large: 0ms;
    --sid-delay-xlarge: 0ms;
    --sid-delay-xxlarge: 0ms;
  }
}
`;

// Utility function to inject CSS variables
export const injectSidTokens = (): void => {
  if (typeof document === 'undefined') return;
  
  const existingStyle = document.getElementById('sid-tokens');
  if (existingStyle) {
    existingStyle.textContent = sidCssVars;
    return;
  }

  const style = document.createElement('style');
  style.id = 'sid-tokens';
  style.textContent = sidCssVars;
  document.head.appendChild(style);
};

// Export for Storybook and other tools
export { sidCssVars as tokensCss };
