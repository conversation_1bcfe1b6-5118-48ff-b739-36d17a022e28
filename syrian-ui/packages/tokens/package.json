{"name": "@sid/tokens", "version": "0.1.0", "description": "Syrian Identity Design Tokens - Cultural color palettes, typography, and spacing systems", "sideEffects": false, "license": "MIT", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./colors": {"types": "./dist/colors.d.ts", "import": "./dist/colors.mjs", "require": "./dist/colors.js"}, "./typography": {"types": "./dist/typography.d.ts", "import": "./dist/typography.mjs", "require": "./dist/typography.js"}, "./spacing": {"types": "./dist/spacing.d.ts", "import": "./dist/spacing.mjs", "require": "./dist/spacing.js"}, "./css": {"types": "./dist/css-vars.d.ts", "import": "./dist/css-vars.mjs", "require": "./dist/css-vars.js"}}, "files": ["dist/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint . --max-warnings 0", "clean": "rm -rf .turbo node_modules dist"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "^8.57.0", "tsup": "^8.0.2", "typescript": "5.5.4"}, "publishConfig": {"access": "public"}, "keywords": ["design-tokens", "syrian", "rtl", "arabic", "cultural-design", "accessibility"]}