/**
 * Syrian Identity Alert Component
 * 
 * A beautiful alert component with Syrian cultural design:
 * - RTL-first design with proper Arabic text support
 * - Cultural color variants inspired by Syrian heritage
 * - Enhanced accessibility with proper ARIA attributes
 * - Damascus-inspired styling with cultural authenticity
 * - Dismissible functionality with smooth animations
 */

import * as React from 'react';

export type AlertVariant = 'info' | 'success' | 'warning' | 'error' | 'cultural';
export type AlertSize = 'sm' | 'md' | 'lg';

export interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Visual style variant inspired by Syrian design.
   * @default 'info'
   */
  variant?: AlertVariant;
  
  /**
   * Size scale for different use cases.
   * @default 'md'
   */
  size?: AlertSize;
  
  /**
   * Alert title (supports Arabic).
   */
  title?: string;
  
  /**
   * Icon to display at the start of the alert (respects RTL).
   */
  icon?: React.ReactNode;
  
  /**
   * Whether the alert can be dismissed.
   * @default false
   */
  dismissible?: boolean;
  
  /**
   * Callback when alert is dismissed.
   */
  onDismiss?: () => void;
  
  /**
   * Action buttons or links to display.
   */
  actions?: React.ReactNode;
  
  /**
   * Text direction override. Usually auto-detected from content.
   * @default 'auto'
   */
  dir?: 'rtl' | 'ltr' | 'auto';
}

/**
 * Default Icons for Alert Variants
 */
const InfoIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
  </svg>
);

const SuccessIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
  </svg>
);

const WarningIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
  </svg>
);

const ErrorIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
  </svg>
);

const CloseIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M4.646 4.646a.5.5 0 01.708 0L8 7.293l2.646-2.647a.5.5 0 01.708.708L8.707 8l2.647 2.646a.5.5 0 01-.708.708L8 8.707l-2.646 2.647a.5.5 0 01-.708-.708L7.293 8 4.646 5.354a.5.5 0 010-.708z"/>
  </svg>
);

/**
 * Syrian Identity Alert Component
 * 
 * @example
 * ```tsx
 * // Arabic alert with cultural styling
 * <Alert 
 *   variant="cultural"
 *   title="تنبيه مهم"
 *   dismissible
 *   dir="rtl"
 * >
 *   هذا تنبيه مهم يتطلب انتباهك
 * </Alert>
 * 
 * // Success alert with actions
 * <Alert
 *   variant="success"
 *   title="تم بنجاح"
 *   actions={<Button size="sm">عرض التفاصيل</Button>}
 * >
 *   تم حفظ البيانات بنجاح
 * </Alert>
 * ```
 */
export const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  (
    {
      variant = 'info',
      size = 'md',
      title,
      icon,
      dismissible = false,
      onDismiss,
      actions,
      dir = 'auto',
      className,
      children,
      ...rest
    },
    ref
  ) => {
    const [isVisible, setIsVisible] = React.useState(true);
    
    const handleDismiss = () => {
      setIsVisible(false);
      onDismiss?.();
    };
    
    // Get default icon based on variant
    const getDefaultIcon = () => {
      switch (variant) {
        case 'success':
          return <SuccessIcon />;
        case 'warning':
          return <WarningIcon />;
        case 'error':
          return <ErrorIcon />;
        case 'cultural':
          return <InfoIcon />;
        default:
          return <InfoIcon />;
      }
    };
    
    const displayIcon = icon || getDefaultIcon();
    
    // Get alert styles
    const getAlertStyles = (): React.CSSProperties => {
      const sizeMap = {
        sm: {
          padding: 'var(--sid-space-3)',
          fontSize: 'var(--sid-text-sm)',
          gap: 'var(--sid-space-2)'
        },
        md: {
          padding: 'var(--sid-space-4)',
          fontSize: 'var(--sid-text-base)',
          gap: 'var(--sid-space-3)'
        },
        lg: {
          padding: 'var(--sid-space-6)',
          fontSize: 'var(--sid-text-lg)',
          gap: 'var(--sid-space-4)'
        }
      };
      
      const baseStyles: React.CSSProperties = {
        display: isVisible ? 'flex' : 'none',
        alignItems: 'flex-start',
        gap: sizeMap[size].gap,
        padding: sizeMap[size].padding,
        borderRadius: 'var(--sid-radius-lg)',
        border: '1px solid',
        fontFamily: dir === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
        fontSize: sizeMap[size].fontSize,
        lineHeight: 'var(--sid-leading-relaxed)',
        transition: 'all 200ms ease',
        flexDirection: dir === 'rtl' ? 'row-reverse' : 'row',
      };

      // Variant-based colors
      switch (variant) {
        case 'cultural':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-wheat-50)',
            borderColor: 'var(--sid-wheat-400)',
            color: 'var(--sid-charcoal-900)',
          };
        case 'success':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-forest-50)',
            borderColor: 'var(--sid-flag-green)',
            color: 'var(--sid-forest-900)',
          };
        case 'warning':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-wheat-100)',
            borderColor: 'var(--sid-wheat-600)',
            color: 'var(--sid-wheat-900)',
          };
        case 'error':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-umber-50)',
            borderColor: 'var(--sid-flag-red)',
            color: 'var(--sid-umber-900)',
          };
        default: // info
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-forest-50)',
            borderColor: 'var(--sid-forest-400)',
            color: 'var(--sid-forest-900)',
          };
      }
    };

    const getIconStyles = (): React.CSSProperties => ({
      flexShrink: 0,
      marginBlockStart: title ? '0' : '2px', // Align with text baseline
    });

    const getTitleStyles = (): React.CSSProperties => ({
      fontWeight: 'var(--sid-font-semibold)',
      marginBlockEnd: children ? 'var(--sid-space-1)' : '0',
      color: 'inherit',
    });

    const getContentStyles = (): React.CSSProperties => ({
      flex: 1,
      minWidth: 0, // Allow text wrapping
    });

    const getActionsStyles = (): React.CSSProperties => ({
      marginBlockStart: 'var(--sid-space-2)',
      display: 'flex',
      gap: 'var(--sid-space-2)',
      flexDirection: dir === 'rtl' ? 'row-reverse' : 'row',
    });

    const getDismissButtonStyles = (): React.CSSProperties => ({
      background: 'none',
      border: 'none',
      cursor: 'pointer',
      padding: 'var(--sid-space-1)',
      borderRadius: 'var(--sid-radius-sm)',
      color: 'inherit',
      opacity: 0.7,
      transition: 'opacity 150ms ease',
      flexShrink: 0,
      marginInlineStart: 'var(--sid-space-2)',
    });

    if (!isVisible) return null;

    return (
      <div
        ref={ref}
        dir={dir}
        role="alert"
        style={getAlertStyles()}
        className={className}
        {...rest}
      >
        {/* Icon */}
        <div style={getIconStyles()}>
          {displayIcon}
        </div>
        
        {/* Content */}
        <div style={getContentStyles()}>
          {title && (
            <div style={getTitleStyles()}>
              {title}
            </div>
          )}
          
          {children && (
            <div>
              {children}
            </div>
          )}
          
          {actions && (
            <div style={getActionsStyles()}>
              {actions}
            </div>
          )}
        </div>
        
        {/* Dismiss button */}
        {dismissible && (
          <button
            type="button"
            onClick={handleDismiss}
            style={getDismissButtonStyles()}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = '1';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '0.7';
            }}
            aria-label={dir === 'rtl' ? 'إغلاق التنبيه' : 'Close alert'}
          >
            <CloseIcon />
          </button>
        )}
      </div>
    );
  }
);

Alert.displayName = 'Alert';
