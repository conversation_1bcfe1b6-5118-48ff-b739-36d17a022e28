/**
 * Syrian Identity Badge Component
 * 
 * A beautiful badge component with Syrian cultural design:
 * - RTL-first design with proper Arabic text support
 * - Cultural color variants inspired by Syrian heritage
 * - Multiple sizes and shapes for different use cases
 * - Enhanced accessibility with proper semantic meaning
 * - Damascus-inspired styling with cultural authenticity
 */

import * as React from 'react';

export type BadgeVariant = 'default' | 'cultural' | 'success' | 'warning' | 'error' | 'info';
export type BadgeSize = 'sm' | 'md' | 'lg';
export type BadgeShape = 'rounded' | 'pill' | 'square';

export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  /**
   * Visual style variant inspired by Syrian design.
   * @default 'default'
   */
  variant?: BadgeVariant;
  
  /**
   * Size scale for different use cases.
   * @default 'md'
   */
  size?: BadgeSize;
  
  /**
   * Shape of the badge.
   * @default 'rounded'
   */
  shape?: BadgeShape;
  
  /**
   * Icon to display before the text (respects RTL).
   */
  startIcon?: React.ReactNode;
  
  /**
   * Icon to display after the text (respects RTL).
   */
  endIcon?: React.ReactNode;
  
  /**
   * Whether the badge should have a dot indicator.
   * @default false
   */
  dot?: boolean;
  
  /**
   * Text direction override. Usually auto-detected from content.
   * @default 'auto'
   */
  dir?: 'rtl' | 'ltr' | 'auto';
}

/**
 * Syrian Identity Badge Component
 * 
 * @example
 * ```tsx
 * // Arabic badge with cultural styling
 * <Badge variant="cultural" dir="rtl">
 *   جديد
 * </Badge>
 * 
 * // Status badge with icon
 * <Badge 
 *   variant="success" 
 *   startIcon={<CheckIcon />}
 *   shape="pill"
 * >
 *   مكتمل
 * </Badge>
 * 
 * // Notification badge with dot
 * <Badge variant="error" dot size="sm">
 *   5
 * </Badge>
 * ```
 */
export const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  (
    {
      variant = 'default',
      size = 'md',
      shape = 'rounded',
      startIcon,
      endIcon,
      dot = false,
      dir = 'auto',
      className,
      children,
      ...rest
    },
    ref
  ) => {
    // Get badge styles
    const getBadgeStyles = (): React.CSSProperties => {
      const sizeMap = {
        sm: {
          fontSize: 'var(--sid-text-xs)',
          padding: 'var(--sid-space-1) var(--sid-space-2)',
          minHeight: '20px',
          gap: 'var(--sid-space-1)'
        },
        md: {
          fontSize: 'var(--sid-text-sm)',
          padding: 'var(--sid-space-1_5) var(--sid-space-3)',
          minHeight: '24px',
          gap: 'var(--sid-space-1_5)'
        },
        lg: {
          fontSize: 'var(--sid-text-base)',
          padding: 'var(--sid-space-2) var(--sid-space-4)',
          minHeight: '32px',
          gap: 'var(--sid-space-2)'
        }
      };

      const shapeMap = {
        rounded: 'var(--sid-radius-md)',
        pill: '9999px',
        square: 'var(--sid-radius-sm)'
      };
      
      const baseStyles: React.CSSProperties = {
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontFamily: dir === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
        fontSize: sizeMap[size].fontSize,
        fontWeight: 'var(--sid-font-medium)',
        lineHeight: 1,
        padding: dot ? '0' : sizeMap[size].padding,
        minHeight: dot ? sizeMap[size].minHeight : 'auto',
        minWidth: dot ? sizeMap[size].minHeight : 'auto',
        borderRadius: dot ? '50%' : shapeMap[shape],
        border: 'none',
        outline: 'none',
        textAlign: 'center',
        whiteSpace: 'nowrap',
        userSelect: 'none',
        gap: sizeMap[size].gap,
        flexDirection: dir === 'rtl' ? 'row-reverse' : 'row',
        position: 'relative',
      };

      // Variant-based colors
      switch (variant) {
        case 'cultural':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-wheat-600)',
            color: 'var(--sid-charcoal-0)',
          };
        case 'success':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-flag-green)',
            color: 'var(--sid-charcoal-0)',
          };
        case 'warning':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-wheat-700)',
            color: 'var(--sid-charcoal-0)',
          };
        case 'error':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-flag-red)',
            color: 'var(--sid-charcoal-0)',
          };
        case 'info':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-forest-600)',
            color: 'var(--sid-charcoal-0)',
          };
        default:
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-charcoal-600)',
            color: 'var(--sid-charcoal-0)',
          };
      }
    };

    const getIconStyles = (): React.CSSProperties => ({
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: size === 'sm' ? '12px' : size === 'lg' ? '16px' : '14px',
      height: size === 'sm' ? '12px' : size === 'lg' ? '16px' : '14px',
      flexShrink: 0,
    });

    const getDotStyles = (): React.CSSProperties => ({
      position: 'absolute',
      top: '-2px',
      right: dir === 'rtl' ? 'auto' : '-2px',
      left: dir === 'rtl' ? '-2px' : 'auto',
      width: '8px',
      height: '8px',
      borderRadius: '50%',
      backgroundColor: variant === 'error' ? 'var(--sid-flag-red)' : 
                      variant === 'success' ? 'var(--sid-flag-green)' :
                      variant === 'warning' ? 'var(--sid-wheat-700)' :
                      'var(--sid-forest-600)',
      border: '2px solid var(--sid-bg-primary)',
    });

    return (
      <span
        ref={ref}
        dir={dir}
        style={getBadgeStyles()}
        className={className}
        {...rest}
      >
        {/* Dot indicator */}
        {dot && <span style={getDotStyles()} />}
        
        {/* Start icon */}
        {startIcon && (
          <span style={getIconStyles()}>
            {startIcon}
          </span>
        )}
        
        {/* Badge content */}
        {children}
        
        {/* End icon */}
        {endIcon && (
          <span style={getIconStyles()}>
            {endIcon}
          </span>
        )}
      </span>
    );
  }
);

Badge.displayName = 'Badge';
