/**
 * Syrian Identity Card Component
 * 
 * A beautiful card component with Syrian cultural design:
 * - RTL-first design with proper Arabic content support
 * - Cultural color variants inspired by Syrian architecture
 * - Enhanced accessibility with proper semantic structure
 * - Damascus-inspired shadows and border radius
 * - Flexible layout options for various content types
 */

import * as React from 'react';

export type CardVariant = 'default' | 'cultural' | 'elevated' | 'outlined' | 'filled';
export type CardPadding = 'none' | 'sm' | 'md' | 'lg' | 'xl';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Visual style variant inspired by Syrian design.
   * @default 'default'
   */
  variant?: CardVariant;
  
  /**
   * Internal padding size.
   * @default 'md'
   */
  padding?: CardPadding;
  
  /**
   * Whether the card should be interactive (hover effects).
   * @default false
   */
  interactive?: boolean;
  
  /**
   * Header content (title, actions, etc.).
   */
  header?: React.ReactNode;
  
  /**
   * Footer content (actions, metadata, etc.).
   */
  footer?: React.ReactNode;
  
  /**
   * Text direction override. Usually auto-detected from content.
   * @default 'auto'
   */
  dir?: 'rtl' | 'ltr' | 'auto';
}

/**
 * Syrian Identity Card Component
 * 
 * @example
 * ```tsx
 * // Arabic card with cultural styling
 * <Card 
 *   variant="cultural"
 *   header={<h3>بطاقة سورية</h3>}
 *   footer={<Button>اقرأ المزيد</Button>}
 *   dir="rtl"
 * >
 *   <p>محتوى البطاقة باللغة العربية</p>
 * </Card>
 * 
 * // Interactive card with elevation
 * <Card
 *   variant="elevated"
 *   interactive
 *   padding="lg"
 * >
 *   <h4>Interactive Card</h4>
 *   <p>This card responds to hover interactions</p>
 * </Card>
 * ```
 */
export const Card = React.forwardRef<HTMLDivElement, CardProps>(
  (
    {
      variant = 'default',
      padding = 'md',
      interactive = false,
      header,
      footer,
      dir = 'auto',
      className,
      children,
      ...rest
    },
    ref
  ) => {
    // Get card styles
    const getCardStyles = (): React.CSSProperties => {
      const paddingMap = {
        none: '0',
        sm: 'var(--sid-space-3)',
        md: 'var(--sid-space-4)',
        lg: 'var(--sid-space-6)',
        xl: 'var(--sid-space-8)'
      };
      
      const baseStyles: React.CSSProperties = {
        borderRadius: 'var(--sid-radius-card)',
        transition: interactive ? 'all 200ms ease' : 'none',
        cursor: interactive ? 'pointer' : 'default',
        position: 'relative',
        overflow: 'hidden',
        fontFamily: dir === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
      };

      // Variant-based styling
      switch (variant) {
        case 'cultural':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-wheat-50)',
            border: '1px solid var(--sid-wheat-200)',
            boxShadow: 'var(--sid-shadow-sm)',
            color: 'var(--sid-charcoal-900)',
          };
        case 'elevated':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-bg-primary)',
            border: 'none',
            boxShadow: 'var(--sid-shadow-lg)',
            color: 'var(--sid-text-primary)',
          };
        case 'outlined':
          return {
            ...baseStyles,
            backgroundColor: 'transparent',
            border: '2px solid var(--sid-border-primary)',
            boxShadow: 'none',
            color: 'var(--sid-text-primary)',
          };
        case 'filled':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-bg-secondary)',
            border: 'none',
            boxShadow: 'none',
            color: 'var(--sid-text-primary)',
          };
        default:
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-bg-primary)',
            border: '1px solid var(--sid-border-primary)',
            boxShadow: 'var(--sid-shadow-sm)',
            color: 'var(--sid-text-primary)',
          };
      }
    };

    const getHeaderStyles = (): React.CSSProperties => ({
      padding: padding !== 'none' ? `${paddingMap[padding]} ${paddingMap[padding]} 0` : '0',
      borderBlockEnd: header && (children || footer) ? '1px solid var(--sid-border-primary)' : 'none',
      marginBlockEnd: header && (children || footer) && padding !== 'none' ? paddingMap[padding] : '0',
    });

    const getContentStyles = (): React.CSSProperties => ({
      padding: padding !== 'none' ? paddingMap[padding] : '0',
      paddingBlockStart: header && padding !== 'none' ? '0' : paddingMap[padding],
      paddingBlockEnd: footer && padding !== 'none' ? '0' : paddingMap[padding],
    });

    const getFooterStyles = (): React.CSSProperties => ({
      padding: padding !== 'none' ? `0 ${paddingMap[padding]} ${paddingMap[padding]}` : '0',
      borderBlockStart: footer && (header || children) ? '1px solid var(--sid-border-primary)' : 'none',
      marginBlockStart: footer && (header || children) && padding !== 'none' ? paddingMap[padding] : '0',
    });

    const paddingMap = {
      none: '0',
      sm: 'var(--sid-space-3)',
      md: 'var(--sid-space-4)',
      lg: 'var(--sid-space-6)',
      xl: 'var(--sid-space-8)'
    };

    return (
      <div
        ref={ref}
        dir={dir}
        style={getCardStyles()}
        className={className}
        onMouseEnter={(e) => {
          if (interactive) {
            const target = e.currentTarget as HTMLDivElement;
            target.style.transform = 'translateY(-2px)';
            target.style.boxShadow = variant === 'cultural' 
              ? '0 8px 25px rgba(185, 167, 121, 0.15)' 
              : 'var(--sid-shadow-xl)';
          }
          rest.onMouseEnter?.(e);
        }}
        onMouseLeave={(e) => {
          if (interactive) {
            const target = e.currentTarget as HTMLDivElement;
            target.style.transform = 'translateY(0)';
            target.style.boxShadow = variant === 'elevated' 
              ? 'var(--sid-shadow-lg)' 
              : variant === 'cultural'
              ? 'var(--sid-shadow-sm)'
              : variant === 'outlined' || variant === 'filled'
              ? 'none'
              : 'var(--sid-shadow-sm)';
          }
          rest.onMouseLeave?.(e);
        }}
        {...rest}
      >
        {header && (
          <div style={getHeaderStyles()}>
            {header}
          </div>
        )}
        
        {children && (
          <div style={getContentStyles()}>
            {children}
          </div>
        )}
        
        {footer && (
          <div style={getFooterStyles()}>
            {footer}
          </div>
        )}
      </div>
    );
  }
);

Card.displayName = 'Card';
