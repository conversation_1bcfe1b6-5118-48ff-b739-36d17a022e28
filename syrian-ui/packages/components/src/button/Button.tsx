/**
 * Syrian Identity Button Component
 * 
 * A production-ready button component with:
 * - RTL-first design using logical properties
 * - Syrian cultural color variants
 * - Full accessibility support (WCAG AA)
 * - Arabic typography optimization
 * - Touch-friendly sizing
 * - Loading and disabled states
 * - Icon support with proper spacing
 */

import * as React from 'react';
import { clsx } from 'clsx';
import { useAnimation } from '../animations/AnimationProvider';

export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
export type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * Visual style variant based on Syrian design language.
   * @default 'primary'
   */
  variant?: ButtonVariant;
  
  /**
   * Size scale for different use cases.
   * @default 'md'
   */
  size?: ButtonSize;
  
  /**
   * Icon to display at the start of the button (respects RTL).
   */
  startIcon?: React.ReactNode;
  
  /**
   * Icon to display at the end of the button (respects RTL).
   */
  endIcon?: React.ReactNode;
  
  /**
   * Whether the button should take full width of its container.
   * @default false
   */
  fullWidth?: boolean;
  
  /**
   * Loading state - shows spinner and disables interaction.
   * @default false
   */
  loading?: boolean;
  
  /**
   * Text direction override. Usually auto-detected from content.
   * @default 'auto'
   */
  dir?: 'rtl' | 'ltr' | 'auto';

  /**
   * Enable ripple effect on click
   * @default true
   */
  ripple?: boolean;

  /**
   * Enable geometric pattern hover effect
   * @default false
   */
  geometricHover?: boolean;

  /**
   * Animation style for cultural theming
   * @default 'default'
   */
  animationStyle?: 'default' | 'cultural' | 'minimal';

  /**
   * Button content - can be Arabic or English text.
   */
  children: React.ReactNode;
}

/**
 * Inject CSS animations if not already present
 */
const injectAnimationStyles = () => {
  if (typeof document === 'undefined') return;

  const styleId = 'sid-button-animations';
  if (document.getElementById(styleId)) return;

  const style = document.createElement('style');
  style.id = styleId;
  style.textContent = `
    @keyframes ripple {
      0% {
        transform: scale(0);
        opacity: 0.3;
      }
      100% {
        transform: scale(1);
        opacity: 0;
      }
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    @media (prefers-reduced-motion: reduce) {
      @keyframes ripple {
        0%, 100% {
          transform: scale(0);
          opacity: 0;
        }
      }

      @keyframes spin {
        to {
          transform: rotate(0deg);
        }
      }
    }
  `;
  document.head.appendChild(style);
};

/**
 * Ripple effect component
 */
interface RippleProps {
  x: number;
  y: number;
  size: number;
  color?: string;
}

const Ripple: React.FC<RippleProps> = ({ x, y, size, color = 'currentColor' }) => {
  React.useEffect(() => {
    injectAnimationStyles();
  }, []);

  return (
    <span
      style={{
        position: 'absolute',
        left: x - size / 2,
        top: y - size / 2,
        width: size,
        height: size,
        borderRadius: '50%',
        backgroundColor: color,
        opacity: 0.3,
        transform: 'scale(0)',
        animation: 'ripple 600ms ease-out',
        pointerEvents: 'none',
      }}
    />
  );
};

/**
 * Loading spinner component with cultural animation
 */
const LoadingSpinner: React.FC<{ cultural?: boolean }> = ({ cultural = false }) => {
  if (cultural) {
    // Islamic geometric pattern inspired spinner
    return (
      <span
        style={{
          display: 'inline-block',
          width: '1em',
          height: '1em',
          position: 'relative',
        }}
        aria-hidden="true"
      >
        <span
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            border: '2px solid transparent',
            borderTopColor: 'currentColor',
            borderRadius: '50%',
            animation: 'spin var(--sid-duration-deliberate) var(--sid-ease-rhythmic) infinite',
          }}
        />
        <span
          style={{
            position: 'absolute',
            width: '60%',
            height: '60%',
            top: '20%',
            left: '20%',
            border: '1px solid transparent',
            borderBottomColor: 'currentColor',
            borderRadius: '50%',
            animation: 'spin var(--sid-duration-deliberate) var(--sid-ease-rhythmic) infinite reverse',
          }}
        />
      </span>
    );
  }

  return (
    <span
      style={{
        display: 'inline-block',
        width: '1em',
        height: '1em',
        border: '2px solid transparent',
        borderTopColor: 'currentColor',
        borderRadius: '50%',
        animation: 'spin var(--sid-duration-deliberate) linear infinite'
      }}
      aria-hidden="true"
    />
  );
};

/**
 * Syrian Identity Button Component
 * 
 * @example
 * ```tsx
 * // Arabic primary button
 * <Button variant="primary">متابعة</Button>
 * 
 * // English secondary button with icon
 * <Button variant="secondary" startIcon={<CheckIcon />} dir="ltr">
 *   Continue
 * </Button>
 * 
 * // Loading state
 * <Button loading>جارٍ التحميل...</Button>
 * ```
 */
export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      startIcon,
      endIcon,
      fullWidth = false,
      loading = false,
      disabled = false,
      dir = 'auto',
      ripple = true,
      geometricHover = false,
      animationStyle = 'default',
      className,
      children,
      type = 'button',
      ...rest
    },
    ref
  ) => {
    // Animation context
    const animationContext = React.useContext(React.createContext(null));
    const { prefersReducedMotion, createTransition } = animationContext || {
      prefersReducedMotion: false,
      createTransition: (props: string[], duration = 'normal', easing = 'ease') =>
        props.map(prop => `${prop} var(--sid-duration-${duration}) var(--sid-ease-${easing})`).join(', ')
    };

    // Ripple effect state
    const [ripples, setRipples] = React.useState<RippleProps[]>([]);
    const buttonRef = React.useRef<HTMLButtonElement>(null);

    // Expose ref to parent
    React.useImperativeHandle(ref, () => buttonRef.current!, []);

    // Determine if button has icons for spacing
    const hasIcons = Boolean(startIcon || endIcon || loading);

    // Compute final disabled state
    const isDisabled = disabled || loading;

    // Handle ripple effect
    const handleClick = React.useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
      if (!ripple || prefersReducedMotion || isDisabled) {
        rest.onClick?.(event);
        return;
      }

      const button = buttonRef.current;
      if (!button) {
        rest.onClick?.(event);
        return;
      }

      const rect = button.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height) * 2;
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      const newRipple: RippleProps = {
        x,
        y,
        size,
        color: variant === 'outline' || variant === 'ghost' ? 'currentColor' : 'rgba(255, 255, 255, 0.5)'
      };

      setRipples(prev => [...prev, newRipple]);

      // Remove ripple after animation
      setTimeout(() => {
        setRipples(prev => prev.slice(1));
      }, 600);

      rest.onClick?.(event);
    }, [ripple, prefersReducedMotion, isDisabled, variant, rest]);



    // Get variant styles with animation enhancements
    const getVariantStyles = (): React.CSSProperties => {
      const transitionProperties = ['background-color', 'border-color', 'color', 'box-shadow'];

      if (animationStyle === 'cultural') {
        transitionProperties.push('transform', 'filter');
      } else if (animationStyle === 'default') {
        transitionProperties.push('transform');
      }

      const transition = prefersReducedMotion
        ? 'none'
        : createTransition(transitionProperties, 'fast', animationStyle === 'cultural' ? 'calligraphy' : 'ease');

      const baseStyles: React.CSSProperties = {
        fontFamily: 'var(--sid-font-arabic)',
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        paddingBlock: 'var(--sid-space-2)',
        paddingInline: 'var(--sid-space-4)',
        border: '1px solid transparent',
        borderRadius: 'var(--sid-radius-button)',
        fontSize: 'var(--sid-text-base)',
        fontWeight: 'var(--sid-font-medium)',
        lineHeight: 'var(--sid-leading-normal)',
        textDecoration: 'none',
        whiteSpace: 'nowrap',
        inlineSize: fullWidth ? '100%' : 'auto',
        minBlockSize: 'var(--sid-space-44)',
        cursor: isDisabled ? 'not-allowed' : 'pointer',
        userSelect: 'none',
        transition,
        boxShadow: 'var(--sid-shadow-button)',
        opacity: isDisabled ? 0.5 : 1,
        gap: hasIcons ? 'var(--sid-space-2)' : undefined,
        overflow: 'hidden', // For ripple effect
        // Cultural animation enhancements
        ...(animationStyle === 'cultural' && !prefersReducedMotion && {
          backgroundImage: geometricHover
            ? 'radial-gradient(circle at center, transparent 0%, transparent 50%, rgba(255,255,255,0.1) 100%)'
            : undefined,
          backgroundSize: geometricHover ? '200% 200%' : undefined,
          backgroundPosition: geometricHover ? 'center' : undefined,
        })
      };

      switch (variant) {
        case 'primary':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-forest-600)',
            color: 'var(--sid-text-on-color)',
            borderColor: 'var(--sid-forest-600)'
          };
        case 'secondary':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-wheat-400)',
            color: 'var(--sid-charcoal-900)',
            borderColor: 'var(--sid-wheat-400)'
          };
        case 'outline':
          return {
            ...baseStyles,
            backgroundColor: 'transparent',
            color: 'var(--sid-forest-600)',
            borderColor: 'var(--sid-forest-600)'
          };
        case 'ghost':
          return {
            ...baseStyles,
            backgroundColor: 'transparent',
            color: 'var(--sid-forest-600)',
            borderColor: 'transparent',
            boxShadow: 'none'
          };
        case 'destructive':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-flag-red)',
            color: 'var(--sid-text-on-color)',
            borderColor: 'var(--sid-flag-red)'
          };
        default:
          return baseStyles;
      }
    };
    
    return (
      <>
        <button
          ref={buttonRef}
          type={type}
          dir={dir}
          disabled={isDisabled}
          aria-disabled={isDisabled || undefined}
          aria-busy={loading || undefined}
          style={getVariantStyles()}
          className={className}
          onClick={handleClick}
          {...rest}
        >
          {/* Start icon or loading spinner */}
          {loading ? (
            <LoadingSpinner cultural={animationStyle === 'cultural'} />
          ) : startIcon ? (
            <span style={{ inlineSize: '1em', blockSize: '1em', flexShrink: 0 }} aria-hidden="true">
              {startIcon}
            </span>
          ) : null}

          {/* Button text content */}
          {children}

          {/* End icon (only if not loading) */}
          {!loading && endIcon && (
            <span style={{ inlineSize: '1em', blockSize: '1em', flexShrink: 0 }} aria-hidden="true">
              {endIcon}
            </span>
          )}

          {/* Ripple effects */}
          {ripples.map((ripple, index) => (
            <Ripple key={index} {...ripple} />
          ))}
        </button>


      </>
    );
  }
);

Button.displayName = 'Button';
