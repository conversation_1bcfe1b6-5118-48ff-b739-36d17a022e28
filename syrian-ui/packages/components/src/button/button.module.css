/**
 * Syrian Identity Button Component Styles
 * 
 * Features:
 * - RTL-first design using logical properties
 * - Syrian cultural color palette
 * - Accessibility-compliant focus states
 * - Arabic typography optimization
 * - Touch-friendly sizing
 */

.root {
  /* Base styles */
  font-family: var(--sid-font-arabic);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  
  /* Use logical properties for RTL support */
  padding-block: var(--sid-space-2);
  padding-inline: var(--sid-space-4);
  
  /* Border and shape */
  border: 1px solid transparent;
  border-radius: var(--sid-radius-button);
  
  /* Typography */
  font-size: var(--sid-text-base);
  font-weight: var(--sid-font-medium);
  line-height: var(--sid-leading-normal);
  text-decoration: none;
  white-space: nowrap;
  
  /* Layout */
  inline-size: auto;
  min-block-size: var(--sid-space-44); /* 44px minimum touch target */
  
  /* Interaction */
  cursor: pointer;
  user-select: none;
  
  /* Transitions */
  transition: 
    background-color 150ms ease,
    border-color 150ms ease,
    color 150ms ease,
    box-shadow 150ms ease,
    transform 100ms ease;
  
  /* Default shadow */
  box-shadow: var(--sid-shadow-button);
}

/* Focus styles for accessibility */
.root:focus-visible {
  outline: none;
  box-shadow: var(--sid-shadow-focus);
  z-index: 1;
}

/* Active state */
.root:active {
  transform: translateY(1px);
}

/* Disabled state */
.root:disabled,
.root[aria-disabled="true"] {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Loading state */
.root[aria-busy="true"] {
  cursor: wait;
}

/* === VARIANTS === */

/* Primary - Syrian forest green */
.primary {
  background-color: var(--sid-forest-600);
  color: var(--sid-text-on-color);
  border-color: var(--sid-forest-600);
}

.primary:hover:not(:disabled):not([aria-disabled="true"]) {
  background-color: var(--sid-forest-700);
  border-color: var(--sid-forest-700);
}

.primary:active {
  background-color: var(--sid-forest-800);
  border-color: var(--sid-forest-800);
}

/* Secondary - Syrian wheat */
.secondary {
  background-color: var(--sid-wheat-400);
  color: var(--sid-charcoal-900);
  border-color: var(--sid-wheat-400);
}

.secondary:hover:not(:disabled):not([aria-disabled="true"]) {
  background-color: var(--sid-wheat-500);
  border-color: var(--sid-wheat-500);
}

.secondary:active {
  background-color: var(--sid-wheat-600);
  border-color: var(--sid-wheat-600);
}

/* Outline - Forest outline */
.outline {
  background-color: transparent;
  color: var(--sid-forest-600);
  border-color: var(--sid-forest-600);
}

.outline:hover:not(:disabled):not([aria-disabled="true"]) {
  background-color: var(--sid-forest-50);
  color: var(--sid-forest-700);
  border-color: var(--sid-forest-700);
}

.outline:active {
  background-color: var(--sid-forest-100);
}

/* Ghost - Minimal styling */
.ghost {
  background-color: transparent;
  color: var(--sid-forest-600);
  border-color: transparent;
  box-shadow: none;
}

.ghost:hover:not(:disabled):not([aria-disabled="true"]) {
  background-color: var(--sid-forest-50);
  color: var(--sid-forest-700);
}

.ghost:active {
  background-color: var(--sid-forest-100);
}

/* Destructive - Syrian flag red */
.destructive {
  background-color: var(--sid-flag-red);
  color: var(--sid-text-on-color);
  border-color: var(--sid-flag-red);
}

.destructive:hover:not(:disabled):not([aria-disabled="true"]) {
  background-color: var(--sid-umber-600);
  border-color: var(--sid-umber-600);
}

.destructive:active {
  background-color: var(--sid-umber-700);
  border-color: var(--sid-umber-700);
}

/* === SIZES === */

.xs {
  padding-block: var(--sid-space-1);
  padding-inline: var(--sid-space-2);
  font-size: var(--sid-text-xs);
  min-block-size: var(--sid-space-32); /* 32px */
}

.sm {
  padding-block: var(--sid-space-1_5);
  padding-inline: var(--sid-space-3);
  font-size: var(--sid-text-sm);
  min-block-size: var(--sid-space-36); /* 36px */
}

.md {
  /* Default size - already defined in .root */
}

.lg {
  padding-block: var(--sid-space-3);
  padding-inline: var(--sid-space-6);
  font-size: var(--sid-text-lg);
  min-block-size: var(--sid-space-48); /* 48px */
}

.xl {
  padding-block: var(--sid-space-4);
  padding-inline: var(--sid-space-8);
  font-size: var(--sid-text-xl);
  min-block-size: var(--sid-space-56); /* 56px */
}

/* === ICON HANDLING === */

.withIcon {
  gap: var(--sid-space-2);
}

.withIcon.sm {
  gap: var(--sid-space-1_5);
}

.withIcon.lg {
  gap: var(--sid-space-3);
}

.withIcon.xl {
  gap: var(--sid-space-4);
}

/* Icon sizing */
.icon {
  inline-size: 1em;
  block-size: 1em;
  flex-shrink: 0;
}

/* Loading spinner */
.spinner {
  inline-size: 1em;
  block-size: 1em;
  border: 2px solid transparent;
  border-block-start-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* === FULL WIDTH === */

.fullWidth {
  inline-size: 100%;
}

/* === RTL SPECIFIC ADJUSTMENTS === */

[dir="rtl"] .root {
  /* Any RTL-specific adjustments if needed */
}

/* === REDUCED MOTION === */

@media (prefers-reduced-motion: reduce) {
  .root {
    transition: none;
  }
  
  .root:active {
    transform: none;
  }
  
  .spinner {
    animation: none;
  }
}

/* === HIGH CONTRAST MODE === */

@media (prefers-contrast: high) {
  .root {
    border-width: 2px;
  }
  
  .ghost {
    border-color: currentColor;
  }
}
