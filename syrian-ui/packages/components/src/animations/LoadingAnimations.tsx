/**
 * Syrian Identity Loading and Progress Animations
 * 
 * Sophisticated loading components with Islamic geometric patterns and Syrian cultural elements:
 * - Geometric spinners based on Islamic art patterns
 * - Progress bars with Syrian flag color progression
 * - Skeleton loaders with RTL-aware layouts
 * - Cultural loading indicators with Damascus-inspired designs
 */

import * as React from 'react';
import { useAnimation } from './AnimationProvider';

// Base Loading Props
interface BaseLoadingProps {
  /**
   * Loading component size
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg' | 'xl';
  
  /**
   * Primary color
   * @default 'var(--sid-forest-500)'
   */
  color?: string;
  
  /**
   * Secondary color for multi-color components
   * @default 'var(--sid-wheat-400)'
   */
  secondaryColor?: string;
  
  /**
   * Animation speed
   * @default 'normal'
   */
  speed?: 'slow' | 'normal' | 'fast';
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
}

// Geometric Spinner Component
export interface GeometricSpinnerProps extends BaseLoadingProps {
  /**
   * Spinner pattern type
   * @default 'islamic'
   */
  pattern?: 'islamic' | 'damascus' | 'star' | 'hexagon' | 'cultural';
}

/**
 * Geometric Spinner Component
 * 
 * Beautiful loading spinner based on Islamic geometric patterns.
 * 
 * @example
 * ```tsx
 * // Islamic pattern spinner
 * <GeometricSpinner 
 *   pattern="islamic"
 *   size="lg"
 *   color="var(--sid-forest-500)"
 *   speed="normal"
 * />
 * 
 * // Damascus pattern spinner
 * <GeometricSpinner 
 *   pattern="damascus"
 *   size="md"
 *   color="#007A3D"
 *   secondaryColor="#CE1126"
 * />
 * ```
 */
export const GeometricSpinner: React.FC<GeometricSpinnerProps> = ({
  size = 'md',
  color = 'var(--sid-forest-500)',
  secondaryColor = 'var(--sid-wheat-400)',
  speed = 'normal',
  pattern = 'islamic',
  className,
  style
}) => {
  const { prefersReducedMotion } = useAnimation();
  const spinnerId = React.useId();
  
  // Get size dimensions
  const getSizeDimensions = () => {
    const sizeMap = {
      sm: 24,
      md: 32,
      lg: 48,
      xl: 64
    };
    return sizeMap[size];
  };
  
  // Get animation duration
  const getAnimationDuration = () => {
    const speedMap = {
      slow: '2s',
      normal: '1.5s',
      fast: '1s'
    };
    return speedMap[speed];
  };
  
  // Inject spinner animation keyframes
  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion) return;
    
    const styleId = `sid-spinner-${pattern}-${spinnerId}`;
    if (document.getElementById(styleId)) return;
    
    const style = document.createElement('style');
    style.id = styleId;
    
    let keyframes = '';
    switch (pattern) {
      case 'islamic':
        keyframes = `
          @keyframes islamicSpin-${spinnerId} {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          @keyframes islamicPulse-${spinnerId} {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
          }
        `;
        break;
      case 'damascus':
        keyframes = `
          @keyframes damascusSpin-${spinnerId} {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
          }
        `;
        break;
      case 'star':
        keyframes = `
          @keyframes starSpin-${spinnerId} {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          @keyframes starTwinkle-${spinnerId} {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }
        `;
        break;
      case 'hexagon':
        keyframes = `
          @keyframes hexagonSpin-${spinnerId} {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          @keyframes hexagonFloat-${spinnerId} {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-4px); }
          }
        `;
        break;
      case 'cultural':
        keyframes = `
          @keyframes culturalSpin-${spinnerId} {
            0% { transform: rotate(0deg) scale(1); opacity: 1; }
            25% { transform: rotate(90deg) scale(1.1); opacity: 0.8; }
            50% { transform: rotate(180deg) scale(0.9); opacity: 0.6; }
            75% { transform: rotate(270deg) scale(1.1); opacity: 0.8; }
            100% { transform: rotate(360deg) scale(1); opacity: 1; }
          }
        `;
        break;
    }
    
    style.textContent = keyframes;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, [pattern, spinnerId, prefersReducedMotion]);
  
  const dimensions = getSizeDimensions();
  const duration = getAnimationDuration();
  
  const containerStyle: React.CSSProperties = {
    width: dimensions,
    height: dimensions,
    display: 'inline-block',
    ...style
  };
  
  const renderPattern = () => {
    switch (pattern) {
      case 'islamic':
        return (
          <svg width={dimensions} height={dimensions} viewBox="0 0 32 32">
            <circle
              cx="16"
              cy="16"
              r="12"
              fill="none"
              stroke={color}
              strokeWidth="2"
              strokeDasharray="8 4"
              style={{
                animation: prefersReducedMotion ? 'none' : `islamicSpin-${spinnerId} ${duration} linear infinite`,
              }}
            />
            <circle
              cx="16"
              cy="16"
              r="6"
              fill="none"
              stroke={secondaryColor}
              strokeWidth="2"
              strokeDasharray="4 2"
              style={{
                animation: prefersReducedMotion ? 'none' : `islamicSpin-${spinnerId} ${duration} linear infinite reverse`,
              }}
            />
            <circle
              cx="16"
              cy="16"
              r="2"
              fill={color}
              style={{
                animation: prefersReducedMotion ? 'none' : `islamicPulse-${spinnerId} 1s ease-in-out infinite`,
              }}
            />
          </svg>
        );
        
      case 'damascus':
        return (
          <svg width={dimensions} height={dimensions} viewBox="0 0 32 32">
            <path
              d="M16 4 L20 12 L28 16 L20 20 L16 28 L12 20 L4 16 L12 12 Z"
              fill={color}
              opacity="0.8"
              style={{
                transformOrigin: '16px 16px',
                animation: prefersReducedMotion ? 'none' : `damascusSpin-${spinnerId} ${duration} ease-in-out infinite`,
              }}
            />
            <path
              d="M16 8 L18 14 L24 16 L18 18 L16 24 L14 18 L8 16 L14 14 Z"
              fill={secondaryColor}
              style={{
                transformOrigin: '16px 16px',
                animation: prefersReducedMotion ? 'none' : `damascusSpin-${spinnerId} ${duration} ease-in-out infinite reverse`,
              }}
            />
          </svg>
        );
        
      case 'star':
        return (
          <svg width={dimensions} height={dimensions} viewBox="0 0 32 32">
            <path
              d="M16 2 L18.5 11.5 L28 16 L18.5 20.5 L16 30 L13.5 20.5 L4 16 L13.5 11.5 Z"
              fill={color}
              style={{
                transformOrigin: '16px 16px',
                animation: prefersReducedMotion ? 'none' : 
                  `starSpin-${spinnerId} ${duration} linear infinite, starTwinkle-${spinnerId} 0.8s ease-in-out infinite`,
              }}
            />
          </svg>
        );
        
      case 'hexagon':
        return (
          <svg width={dimensions} height={dimensions} viewBox="0 0 32 32">
            <polygon
              points="16,4 24,8 24,16 16,20 8,16 8,8"
              fill="none"
              stroke={color}
              strokeWidth="2"
              style={{
                transformOrigin: '16px 12px',
                animation: prefersReducedMotion ? 'none' : 
                  `hexagonSpin-${spinnerId} ${duration} linear infinite, hexagonFloat-${spinnerId} 1s ease-in-out infinite`,
              }}
            />
            <polygon
              points="16,8 20,10 20,14 16,16 12,14 12,10"
              fill={secondaryColor}
              style={{
                transformOrigin: '16px 12px',
                animation: prefersReducedMotion ? 'none' : `hexagonSpin-${spinnerId} ${duration} linear infinite reverse`,
              }}
            />
          </svg>
        );
        
      case 'cultural':
        return (
          <svg width={dimensions} height={dimensions} viewBox="0 0 32 32">
            <g style={{
              transformOrigin: '16px 16px',
              animation: prefersReducedMotion ? 'none' : `culturalSpin-${spinnerId} ${duration} ease-in-out infinite`,
            }}>
              <circle cx="16" cy="8" r="2" fill={color} />
              <circle cx="24" cy="16" r="2" fill={secondaryColor} />
              <circle cx="16" cy="24" r="2" fill={color} />
              <circle cx="8" cy="16" r="2" fill={secondaryColor} />
              <circle cx="20" cy="12" r="1.5" fill={color} opacity="0.7" />
              <circle cx="20" cy="20" r="1.5" fill={secondaryColor} opacity="0.7" />
              <circle cx="12" cy="20" r="1.5" fill={color} opacity="0.7" />
              <circle cx="12" cy="12" r="1.5" fill={secondaryColor} opacity="0.7" />
            </g>
          </svg>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <div className={className} style={containerStyle}>
      {renderPattern()}
    </div>
  );
};

// Progress Bar Component
export interface ProgressBarProps {
  /**
   * Progress value (0-100)
   */
  value: number;

  /**
   * Progress bar size
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Color scheme
   * @default 'cultural'
   */
  colorScheme?: 'cultural' | 'flag' | 'forest' | 'wheat';

  /**
   * Show percentage text
   * @default false
   */
  showPercentage?: boolean;

  /**
   * Animation style
   * @default 'smooth'
   */
  animationStyle?: 'smooth' | 'stepped' | 'cultural';

  /**
   * Direction for RTL support
   * @default 'auto'
   */
  direction?: 'ltr' | 'rtl' | 'auto';

  /**
   * Additional CSS classes
   */
  className?: string;

  /**
   * Container styles
   */
  style?: React.CSSProperties;
}

/**
 * Progress Bar Component
 *
 * Beautiful progress bar with Syrian flag color progression and cultural animations.
 *
 * @example
 * ```tsx
 * // Syrian flag colors progress
 * <ProgressBar
 *   value={75}
 *   colorScheme="flag"
 *   size="lg"
 *   showPercentage={true}
 *   animationStyle="cultural"
 * />
 *
 * // Cultural color scheme
 * <ProgressBar
 *   value={50}
 *   colorScheme="cultural"
 *   direction="rtl"
 *   animationStyle="smooth"
 * />
 * ```
 */
export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  size = 'md',
  colorScheme = 'cultural',
  showPercentage = false,
  animationStyle = 'smooth',
  direction = 'auto',
  className,
  style
}) => {
  const { prefersReducedMotion } = useAnimation();
  const progressId = React.useId();
  const [displayValue, setDisplayValue] = React.useState(0);

  // Animate progress value
  React.useEffect(() => {
    if (prefersReducedMotion) {
      setDisplayValue(value);
      return;
    }

    const startValue = displayValue;
    const endValue = Math.max(0, Math.min(100, value));
    const duration = Math.abs(endValue - startValue) * 20; // 20ms per percentage point
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      const easeOutCubic = 1 - Math.pow(1 - progress, 3);
      const currentValue = startValue + (endValue - startValue) * easeOutCubic;

      setDisplayValue(currentValue);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [value, displayValue, prefersReducedMotion]);

  // Get color scheme
  const getColorScheme = () => {
    switch (colorScheme) {
      case 'flag':
        return {
          background: 'linear-gradient(to right, #007A3D 0%, #FFFFFF 50%, #CE1126 100%)',
          backgroundColor: 'var(--sid-charcoal-200)',
        };
      case 'forest':
        return {
          background: 'linear-gradient(to right, var(--sid-forest-300), var(--sid-forest-600))',
          backgroundColor: 'var(--sid-forest-100)',
        };
      case 'wheat':
        return {
          background: 'linear-gradient(to right, var(--sid-wheat-300), var(--sid-wheat-600))',
          backgroundColor: 'var(--sid-wheat-100)',
        };
      default: // cultural
        return {
          background: 'linear-gradient(to right, var(--sid-forest-500), var(--sid-wheat-400), var(--sid-umber-500))',
          backgroundColor: 'var(--sid-charcoal-100)',
        };
    }
  };

  // Get size dimensions
  const getSizeHeight = () => {
    const sizeMap = {
      sm: 4,
      md: 8,
      lg: 12
    };
    return sizeMap[size];
  };

  const colors = getColorScheme();
  const height = getSizeHeight();

  const containerStyle: React.CSSProperties = {
    width: '100%',
    height: height,
    backgroundColor: colors.backgroundColor,
    borderRadius: height / 2,
    overflow: 'hidden',
    position: 'relative',
    direction: direction === 'auto' ? undefined : direction,
    ...style
  };

  const progressStyle: React.CSSProperties = {
    height: '100%',
    width: `${displayValue}%`,
    background: colors.background,
    borderRadius: height / 2,
    transition: prefersReducedMotion ? 'none' :
      animationStyle === 'cultural' ? 'width var(--sid-duration-comfortable) var(--sid-ease-calligraphy)' :
      animationStyle === 'stepped' ? 'width 0.1s steps(10)' :
      'width 0.3s ease-out',
    position: 'relative',
  };

  const percentageStyle: React.CSSProperties = {
    position: 'absolute',
    top: '50%',
    right: direction === 'rtl' ? 'auto' : '8px',
    left: direction === 'rtl' ? '8px' : 'auto',
    transform: 'translateY(-50%)',
    fontSize: '0.75rem',
    fontWeight: 'var(--sid-font-medium)',
    color: 'var(--sid-charcoal-700)',
    textShadow: '0 1px 2px rgba(255, 255, 255, 0.8)',
  };

  return (
    <div className={className} style={containerStyle}>
      <div style={progressStyle} />
      {showPercentage && (
        <div style={percentageStyle}>
          {Math.round(displayValue)}%
        </div>
      )}
    </div>
  );
};

// Skeleton Loader Component
export interface SkeletonLoaderProps {
  /**
   * Skeleton variant
   * @default 'text'
   */
  variant?: 'text' | 'rectangular' | 'circular' | 'card' | 'list';

  /**
   * Width of skeleton
   */
  width?: string | number;

  /**
   * Height of skeleton
   */
  height?: string | number;

  /**
   * Number of lines for text variant
   * @default 1
   */
  lines?: number;

  /**
   * Animation style
   * @default 'wave'
   */
  animation?: 'wave' | 'pulse' | 'cultural' | 'none';

  /**
   * Direction for RTL support
   * @default 'auto'
   */
  direction?: 'ltr' | 'rtl' | 'auto';

  /**
   * Additional CSS classes
   */
  className?: string;

  /**
   * Container styles
   */
  style?: React.CSSProperties;
}

/**
 * Skeleton Loader Component
 *
 * Beautiful skeleton loading placeholders with RTL support and cultural animations.
 *
 * @example
 * ```tsx
 * // Text skeleton with cultural animation
 * <SkeletonLoader
 *   variant="text"
 *   lines={3}
 *   animation="cultural"
 *   direction="rtl"
 * />
 *
 * // Card skeleton
 * <SkeletonLoader
 *   variant="card"
 *   width="300px"
 *   height="200px"
 *   animation="wave"
 * />
 * ```
 */
export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  variant = 'text',
  width,
  height,
  lines = 1,
  animation = 'wave',
  direction = 'auto',
  className,
  style
}) => {
  const { prefersReducedMotion } = useAnimation();
  const skeletonId = React.useId();

  // Inject skeleton animation keyframes
  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion || animation === 'none') return;

    const styleId = `sid-skeleton-${animation}-${skeletonId}`;
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;

    let keyframes = '';
    switch (animation) {
      case 'wave':
        keyframes = `
          @keyframes skeletonWave-${skeletonId} {
            0% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
            100% { transform: translateX(100%); }
          }
        `;
        break;
      case 'pulse':
        keyframes = `
          @keyframes skeletonPulse-${skeletonId} {
            0% { opacity: 1; }
            50% { opacity: 0.4; }
            100% { opacity: 1; }
          }
        `;
        break;
      case 'cultural':
        keyframes = `
          @keyframes skeletonCultural-${skeletonId} {
            0% { opacity: 0.6; transform: scaleX(1); }
            25% { opacity: 0.8; transform: scaleX(1.02); }
            50% { opacity: 0.4; transform: scaleX(0.98); }
            75% { opacity: 0.7; transform: scaleX(1.01); }
            100% { opacity: 0.6; transform: scaleX(1); }
          }
        `;
        break;
    }

    style.textContent = keyframes;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [animation, skeletonId, prefersReducedMotion]);

  const getSkeletonStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      backgroundColor: 'var(--sid-charcoal-200)',
      borderRadius: '4px',
      position: 'relative',
      overflow: 'hidden',
      direction: direction === 'auto' ? undefined : direction,
    };

    if (animation === 'wave' && !prefersReducedMotion) {
      baseStyle.background = `
        linear-gradient(90deg,
          var(--sid-charcoal-200) 25%,
          var(--sid-charcoal-100) 50%,
          var(--sid-charcoal-200) 75%
        )
      `;
      baseStyle.backgroundSize = '200% 100%';
      baseStyle.animation = `skeletonWave-${skeletonId} 1.6s ease-in-out infinite`;
    } else if (animation === 'pulse' && !prefersReducedMotion) {
      baseStyle.animation = `skeletonPulse-${skeletonId} 2s ease-in-out infinite`;
    } else if (animation === 'cultural' && !prefersReducedMotion) {
      baseStyle.animation = `skeletonCultural-${skeletonId} var(--sid-duration-contemplative) var(--sid-ease-calligraphy) infinite`;
    }

    return baseStyle;
  };

  const renderSkeleton = () => {
    switch (variant) {
      case 'text':
        return (
          <div style={{ direction: direction === 'auto' ? undefined : direction }}>
            {Array.from({ length: lines }, (_, index) => (
              <div
                key={index}
                style={{
                  ...getSkeletonStyle(),
                  width: width || (index === lines - 1 ? '60%' : '100%'),
                  height: height || '1em',
                  marginBottom: index < lines - 1 ? '0.5em' : 0,
                }}
              />
            ))}
          </div>
        );

      case 'rectangular':
        return (
          <div
            style={{
              ...getSkeletonStyle(),
              width: width || '100%',
              height: height || '200px',
            }}
          />
        );

      case 'circular':
        return (
          <div
            style={{
              ...getSkeletonStyle(),
              width: width || '40px',
              height: height || width || '40px',
              borderRadius: '50%',
            }}
          />
        );

      case 'card':
        return (
          <div style={{ direction: direction === 'auto' ? undefined : direction }}>
            <div
              style={{
                ...getSkeletonStyle(),
                width: '100%',
                height: '120px',
                marginBottom: '1rem',
              }}
            />
            <div
              style={{
                ...getSkeletonStyle(),
                width: '80%',
                height: '1.2em',
                marginBottom: '0.5rem',
              }}
            />
            <div
              style={{
                ...getSkeletonStyle(),
                width: '60%',
                height: '1em',
              }}
            />
          </div>
        );

      case 'list':
        return (
          <div style={{ direction: direction === 'auto' ? undefined : direction }}>
            {Array.from({ length: 3 }, (_, index) => (
              <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
                <div
                  style={{
                    ...getSkeletonStyle(),
                    width: '40px',
                    height: '40px',
                    borderRadius: '50%',
                    marginRight: direction === 'rtl' ? 0 : '1rem',
                    marginLeft: direction === 'rtl' ? '1rem' : 0,
                  }}
                />
                <div style={{ flex: 1 }}>
                  <div
                    style={{
                      ...getSkeletonStyle(),
                      width: '70%',
                      height: '1em',
                      marginBottom: '0.5rem',
                    }}
                  />
                  <div
                    style={{
                      ...getSkeletonStyle(),
                      width: '50%',
                      height: '0.8em',
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={className} style={style}>
      {renderSkeleton()}
    </div>
  );
};
