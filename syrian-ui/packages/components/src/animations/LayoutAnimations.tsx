/**
 * Syrian Identity Layout Animation Components
 * 
 * Beautiful layout animations with RTL support and Syrian cultural design principles:
 * - Card reveal animations with cultural timing
 * - Staggered list animations for Arabic content
 * - Page transition effects with Syrian design elements
 * - Container animations with Islamic geometric principles
 */

import * as React from 'react';
import { useAnimation } from './AnimationProvider';

// Card Reveal Animation Component
export interface CardRevealProps {
  /**
   * Card content
   */
  children: React.ReactNode;
  
  /**
   * Reveal animation type
   * @default 'slideUp'
   */
  animation?: 'slideUp' | 'slideDown' | 'slideLeft' | 'slideRight' | 'scale' | 'fade' | 'cultural';
  
  /**
   * Animation duration
   * @default 'comfortable'
   */
  duration?: 'fast' | 'normal' | 'comfortable' | 'slow' | 'contemplative';
  
  /**
   * Animation delay
   * @default 0
   */
  delay?: number;
  
  /**
   * Whether card is visible (for triggering animation)
   * @default true
   */
  visible?: boolean;
  
  /**
   * Easing function
   * @default 'graceful'
   */
  easing?: 'ease' | 'graceful' | 'calligraphy' | 'architectural';
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
  
  /**
   * Callback when animation completes
   */
  onAnimationComplete?: () => void;
}

/**
 * Card Reveal Animation Component
 * 
 * Creates beautiful card reveal animations with RTL support and cultural timing.
 * 
 * @example
 * ```tsx
 * // Cultural card reveal
 * <CardReveal 
 *   animation="cultural"
 *   duration="contemplative"
 *   easing="calligraphy"
 * >
 *   <div>Card content with Arabic text</div>
 * </CardReveal>
 * 
 * // RTL slide animation
 * <CardReveal 
 *   animation="slideRight"
 *   duration="comfortable"
 *   delay={200}
 * >
 *   <div dir="rtl">محتوى البطاقة</div>
 * </CardReveal>
 * ```
 */
export const CardReveal: React.FC<CardRevealProps> = ({
  children,
  animation = 'slideUp',
  duration = 'comfortable',
  delay = 0,
  visible = true,
  easing = 'graceful',
  className,
  style,
  onAnimationComplete
}) => {
  const { prefersReducedMotion } = useAnimation();
  const [isVisible, setIsVisible] = React.useState(!visible);
  const animationId = React.useId();
  
  React.useEffect(() => {
    if (visible && !isVisible) {
      const timer = setTimeout(() => {
        setIsVisible(true);
        if (onAnimationComplete) {
          const animationDuration = prefersReducedMotion ? 0 : 
            duration === 'fast' ? 150 :
            duration === 'normal' ? 250 :
            duration === 'comfortable' ? 500 :
            duration === 'slow' ? 750 : 1500;
          
          setTimeout(onAnimationComplete, animationDuration);
        }
      }, delay);
      
      return () => clearTimeout(timer);
    }
  }, [visible, isVisible, delay, onAnimationComplete, duration, prefersReducedMotion]);
  
  // Inject animation keyframes
  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion) return;
    
    const styleId = `sid-card-${animation}-${animationId}`;
    if (document.getElementById(styleId)) return;
    
    const style = document.createElement('style');
    style.id = styleId;
    
    let keyframes = '';
    switch (animation) {
      case 'slideUp':
        keyframes = `
          @keyframes slideUp-${animationId} {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
          }
        `;
        break;
      case 'slideDown':
        keyframes = `
          @keyframes slideDown-${animationId} {
            from { transform: translateY(-30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
          }
        `;
        break;
      case 'slideLeft':
        keyframes = `
          @keyframes slideLeft-${animationId} {
            from { transform: translateX(30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
          }
        `;
        break;
      case 'slideRight':
        keyframes = `
          @keyframes slideRight-${animationId} {
            from { transform: translateX(-30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
          }
        `;
        break;
      case 'scale':
        keyframes = `
          @keyframes scale-${animationId} {
            from { transform: scale(0.8); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
          }
        `;
        break;
      case 'fade':
        keyframes = `
          @keyframes fade-${animationId} {
            from { opacity: 0; }
            to { opacity: 1; }
          }
        `;
        break;
      case 'cultural':
        keyframes = `
          @keyframes cultural-${animationId} {
            0% { transform: scale(0.8) rotate(-5deg); opacity: 0; }
            50% { transform: scale(1.05) rotate(2deg); opacity: 0.7; }
            100% { transform: scale(1) rotate(0deg); opacity: 1; }
          }
        `;
        break;
    }
    
    style.textContent = keyframes;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, [animation, animationId, prefersReducedMotion]);
  
  const cardStyle: React.CSSProperties = {
    opacity: prefersReducedMotion ? (isVisible ? 1 : 0) : undefined,
    animation: prefersReducedMotion ? 'none' : 
      isVisible ? `${animation}-${animationId} var(--sid-duration-${duration}) var(--sid-ease-${easing}) forwards` : 'none',
    ...style
  };
  
  return (
    <div className={className} style={cardStyle}>
      {children}
    </div>
  );
};

// Staggered List Animation Component
export interface StaggeredListProps {
  /**
   * List items
   */
  children: React.ReactNode[];
  
  /**
   * Stagger delay between items
   * @default 100
   */
  staggerDelay?: number;
  
  /**
   * Animation type for each item
   * @default 'slideUp'
   */
  itemAnimation?: 'slideUp' | 'slideDown' | 'slideLeft' | 'slideRight' | 'scale' | 'fade' | 'cultural';
  
  /**
   * Animation duration for each item
   * @default 'normal'
   */
  duration?: 'fast' | 'normal' | 'comfortable' | 'slow' | 'contemplative';
  
  /**
   * Whether list should animate on mount
   * @default true
   */
  animateOnMount?: boolean;
  
  /**
   * Direction for RTL support
   * @default 'auto'
   */
  direction?: 'ltr' | 'rtl' | 'auto';
  
  /**
   * Container element type
   * @default 'div'
   */
  as?: keyof JSX.IntrinsicElements;
  
  /**
   * Additional CSS classes for container
   */
  className?: string;
  
  /**
   * Container styles
   */
  style?: React.CSSProperties;
}

/**
 * Staggered List Animation Component
 * 
 * Creates beautiful staggered animations for lists with RTL support.
 * 
 * @example
 * ```tsx
 * // Arabic content list
 * <StaggeredList 
 *   staggerDelay={150}
 *   itemAnimation="cultural"
 *   direction="rtl"
 * >
 *   {arabicItems.map(item => (
 *     <div key={item.id}>{item.text}</div>
 *   ))}
 * </StaggeredList>
 * 
 * // English content list
 * <StaggeredList 
 *   staggerDelay={100}
 *   itemAnimation="slideLeft"
 *   direction="ltr"
 * >
 *   {englishItems.map(item => (
 *     <div key={item.id}>{item.text}</div>
 *   ))}
 * </StaggeredList>
 * ```
 */
export const StaggeredList: React.FC<StaggeredListProps> = ({
  children,
  staggerDelay = 100,
  itemAnimation = 'slideUp',
  duration = 'normal',
  animateOnMount = true,
  direction = 'auto',
  as: Component = 'div',
  className,
  style
}) => {
  const { prefersReducedMotion } = useAnimation();
  const [mounted, setMounted] = React.useState(false);
  
  React.useEffect(() => {
    if (animateOnMount) {
      setMounted(true);
    }
  }, [animateOnMount]);
  
  const containerStyle: React.CSSProperties = {
    direction: direction === 'auto' ? undefined : direction,
    ...style
  };
  
  return (
    <Component className={className} style={containerStyle}>
      {React.Children.map(children, (child, index) => (
        <CardReveal
          key={index}
          animation={itemAnimation}
          duration={duration}
          delay={prefersReducedMotion ? 0 : (mounted ? staggerDelay * index : 0)}
          visible={mounted}
          easing="graceful"
        >
          {child}
        </CardReveal>
      ))}
    </Component>
  );
};

// Page Transition Component
export interface PageTransitionProps {
  /**
   * Page content
   */
  children: React.ReactNode;

  /**
   * Transition type
   * @default 'slideRight'
   */
  transition?: 'slideLeft' | 'slideRight' | 'slideUp' | 'slideDown' | 'fade' | 'scale' | 'cultural';

  /**
   * Transition duration
   * @default 'slow'
   */
  duration?: 'normal' | 'comfortable' | 'slow' | 'contemplative' | 'ceremonial';

  /**
   * Page key for triggering transitions
   */
  pageKey: string;

  /**
   * Direction for RTL support
   * @default 'auto'
   */
  direction?: 'ltr' | 'rtl' | 'auto';

  /**
   * Additional CSS classes
   */
  className?: string;

  /**
   * Inline styles
   */
  style?: React.CSSProperties;

  /**
   * Callback when transition starts
   */
  onTransitionStart?: () => void;

  /**
   * Callback when transition completes
   */
  onTransitionComplete?: () => void;
}

/**
 * Page Transition Component
 *
 * Creates smooth page transitions with cultural timing and RTL support.
 *
 * @example
 * ```tsx
 * // RTL page transition
 * <PageTransition
 *   pageKey={currentPage}
 *   transition="cultural"
 *   duration="contemplative"
 *   direction="rtl"
 * >
 *   <div>{pageContent}</div>
 * </PageTransition>
 *
 * // LTR page transition
 * <PageTransition
 *   pageKey={currentPage}
 *   transition="slideLeft"
 *   duration="slow"
 *   direction="ltr"
 * >
 *   <div>{pageContent}</div>
 * </PageTransition>
 * ```
 */
export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  transition = 'slideRight',
  duration = 'slow',
  pageKey,
  direction = 'auto',
  className,
  style,
  onTransitionStart,
  onTransitionComplete
}) => {
  const { prefersReducedMotion } = useAnimation();
  const [currentKey, setCurrentKey] = React.useState(pageKey);
  const [isTransitioning, setIsTransitioning] = React.useState(false);
  const transitionId = React.useId();

  React.useEffect(() => {
    if (pageKey !== currentKey) {
      setIsTransitioning(true);
      onTransitionStart?.();

      const transitionDuration = prefersReducedMotion ? 0 :
        duration === 'normal' ? 250 :
        duration === 'comfortable' ? 500 :
        duration === 'slow' ? 750 :
        duration === 'contemplative' ? 1500 : 2000;

      setTimeout(() => {
        setCurrentKey(pageKey);
        setIsTransitioning(false);
        onTransitionComplete?.();
      }, transitionDuration / 2);
    }
  }, [pageKey, currentKey, duration, prefersReducedMotion, onTransitionStart, onTransitionComplete]);

  // Inject transition keyframes
  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion) return;

    const styleId = `sid-page-${transition}-${transitionId}`;
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;

    let keyframes = '';
    switch (transition) {
      case 'slideLeft':
        keyframes = `
          @keyframes pageSlideLeft-${transitionId} {
            0% { transform: translateX(100%); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
          }
          @keyframes pageSlideLeftOut-${transitionId} {
            0% { transform: translateX(0); opacity: 1; }
            100% { transform: translateX(-100%); opacity: 0; }
          }
        `;
        break;
      case 'slideRight':
        keyframes = `
          @keyframes pageSlideRight-${transitionId} {
            0% { transform: translateX(-100%); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
          }
          @keyframes pageSlideRightOut-${transitionId} {
            0% { transform: translateX(0); opacity: 1; }
            100% { transform: translateX(100%); opacity: 0; }
          }
        `;
        break;
      case 'slideUp':
        keyframes = `
          @keyframes pageSlideUp-${transitionId} {
            0% { transform: translateY(100%); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
          }
          @keyframes pageSlideUpOut-${transitionId} {
            0% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(-100%); opacity: 0; }
          }
        `;
        break;
      case 'slideDown':
        keyframes = `
          @keyframes pageSlideDown-${transitionId} {
            0% { transform: translateY(-100%); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
          }
          @keyframes pageSlideDownOut-${transitionId} {
            0% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(100%); opacity: 0; }
          }
        `;
        break;
      case 'fade':
        keyframes = `
          @keyframes pageFade-${transitionId} {
            0% { opacity: 0; }
            100% { opacity: 1; }
          }
          @keyframes pageFadeOut-${transitionId} {
            0% { opacity: 1; }
            100% { opacity: 0; }
          }
        `;
        break;
      case 'scale':
        keyframes = `
          @keyframes pageScale-${transitionId} {
            0% { transform: scale(0.8); opacity: 0; }
            100% { transform: scale(1); opacity: 1; }
          }
          @keyframes pageScaleOut-${transitionId} {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(1.2); opacity: 0; }
          }
        `;
        break;
      case 'cultural':
        keyframes = `
          @keyframes pageCultural-${transitionId} {
            0% { transform: scale(0.9) rotate(-3deg); opacity: 0; filter: blur(5px); }
            50% { transform: scale(1.02) rotate(1deg); opacity: 0.7; filter: blur(2px); }
            100% { transform: scale(1) rotate(0deg); opacity: 1; filter: blur(0px); }
          }
          @keyframes pageCulturalOut-${transitionId} {
            0% { transform: scale(1) rotate(0deg); opacity: 1; filter: blur(0px); }
            50% { transform: scale(0.98) rotate(-1deg); opacity: 0.3; filter: blur(2px); }
            100% { transform: scale(0.9) rotate(3deg); opacity: 0; filter: blur(5px); }
          }
        `;
        break;
    }

    style.textContent = keyframes;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [transition, transitionId, prefersReducedMotion]);

  const pageStyle: React.CSSProperties = {
    direction: direction === 'auto' ? undefined : direction,
    animation: prefersReducedMotion ? 'none' :
      isTransitioning ? `page${transition.charAt(0).toUpperCase() + transition.slice(1)}Out-${transitionId} var(--sid-duration-${duration}) var(--sid-ease-melodic) forwards` :
      `page${transition.charAt(0).toUpperCase() + transition.slice(1)}-${transitionId} var(--sid-duration-${duration}) var(--sid-ease-melodic) forwards`,
    ...style
  };

  return (
    <div className={className} style={pageStyle}>
      {children}
    </div>
  );
};
