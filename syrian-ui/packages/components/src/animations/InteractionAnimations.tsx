/**
 * Syrian Identity Advanced Interaction Animations
 * 
 * Complex interactive animations with Syrian cultural elements:
 * - Drag and drop with cultural visual feedback
 * - Scroll-triggered animations for storytelling
 * - Gesture-based interactions with Syrian design elements
 * - Hover effects with Islamic geometric patterns
 */

import * as React from 'react';
import { useAnimation } from './AnimationProvider';

// Scroll Triggered Animation Component
export interface ScrollTriggeredProps {
  /**
   * Content to animate
   */
  children: React.ReactNode;
  
  /**
   * Animation type
   * @default 'fadeInUp'
   */
  animation?: 'fadeInUp' | 'fadeInDown' | 'fadeInLeft' | 'fadeInRight' | 'scaleIn' | 'cultural';
  
  /**
   * Trigger threshold (0-1)
   * @default 0.1
   */
  threshold?: number;
  
  /**
   * Animation duration
   * @default 'comfortable'
   */
  duration?: 'fast' | 'normal' | 'comfortable' | 'slow' | 'contemplative';
  
  /**
   * Animation delay
   * @default 0
   */
  delay?: number;
  
  /**
   * Whether animation should repeat
   * @default false
   */
  repeat?: boolean;
  
  /**
   * Root margin for intersection observer
   * @default '0px'
   */
  rootMargin?: string;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Container styles
   */
  style?: React.CSSProperties;
  
  /**
   * Callback when animation triggers
   */
  onAnimationStart?: () => void;
  
  /**
   * Callback when animation completes
   */
  onAnimationComplete?: () => void;
}

/**
 * Scroll Triggered Animation Component
 * 
 * Triggers animations when elements come into view, perfect for storytelling.
 * 
 * @example
 * ```tsx
 * // Cultural scroll animation
 * <ScrollTriggered 
 *   animation="cultural"
 *   threshold={0.3}
 *   duration="contemplative"
 *   delay={200}
 * >
 *   <div>Content that animates on scroll</div>
 * </ScrollTriggered>
 * 
 * // Fade in from right (RTL-aware)
 * <ScrollTriggered 
 *   animation="fadeInRight"
 *   threshold={0.2}
 *   repeat={true}
 * >
 *   <div dir="rtl">محتوى يظهر عند التمرير</div>
 * </ScrollTriggered>
 * ```
 */
export const ScrollTriggered: React.FC<ScrollTriggeredProps> = ({
  children,
  animation = 'fadeInUp',
  threshold = 0.1,
  duration = 'comfortable',
  delay = 0,
  repeat = false,
  rootMargin = '0px',
  className,
  style,
  onAnimationStart,
  onAnimationComplete
}) => {
  const { prefersReducedMotion } = useAnimation();
  const [isVisible, setIsVisible] = React.useState(false);
  const [hasAnimated, setHasAnimated] = React.useState(false);
  const elementRef = React.useRef<HTMLDivElement>(null);
  const animationId = React.useId();
  
  // Intersection Observer for scroll detection
  React.useEffect(() => {
    const element = elementRef.current;
    if (!element || prefersReducedMotion) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          if (!hasAnimated || repeat) {
            setIsVisible(true);
            setHasAnimated(true);
            onAnimationStart?.();
            
            // Set animation complete callback
            const animationDuration = 
              duration === 'fast' ? 150 :
              duration === 'normal' ? 250 :
              duration === 'comfortable' ? 500 :
              duration === 'slow' ? 750 : 1500;
            
            setTimeout(() => {
              onAnimationComplete?.();
            }, animationDuration + delay);
          }
        } else if (repeat) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin,
      }
    );
    
    observer.observe(element);
    
    return () => {
      observer.unobserve(element);
    };
  }, [threshold, rootMargin, repeat, hasAnimated, delay, duration, onAnimationStart, onAnimationComplete, prefersReducedMotion]);
  
  // Inject animation keyframes
  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion) return;
    
    const styleId = `sid-scroll-${animation}-${animationId}`;
    if (document.getElementById(styleId)) return;
    
    const style = document.createElement('style');
    style.id = styleId;
    
    let keyframes = '';
    switch (animation) {
      case 'fadeInUp':
        keyframes = `
          @keyframes fadeInUp-${animationId} {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
          }
        `;
        break;
      case 'fadeInDown':
        keyframes = `
          @keyframes fadeInDown-${animationId} {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
          }
        `;
        break;
      case 'fadeInLeft':
        keyframes = `
          @keyframes fadeInLeft-${animationId} {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
          }
        `;
        break;
      case 'fadeInRight':
        keyframes = `
          @keyframes fadeInRight-${animationId} {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
          }
        `;
        break;
      case 'scaleIn':
        keyframes = `
          @keyframes scaleIn-${animationId} {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
          }
        `;
        break;
      case 'cultural':
        keyframes = `
          @keyframes cultural-${animationId} {
            0% { opacity: 0; transform: scale(0.9) rotate(-2deg); filter: blur(3px); }
            50% { opacity: 0.7; transform: scale(1.02) rotate(1deg); filter: blur(1px); }
            100% { opacity: 1; transform: scale(1) rotate(0deg); filter: blur(0px); }
          }
        `;
        break;
    }
    
    style.textContent = keyframes;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, [animation, animationId, prefersReducedMotion]);
  
  const containerStyle: React.CSSProperties = {
    opacity: prefersReducedMotion ? 1 : (isVisible ? 1 : 0),
    animation: prefersReducedMotion ? 'none' : 
      isVisible ? `${animation}-${animationId} var(--sid-duration-${duration}) var(--sid-ease-graceful) forwards` : 'none',
    animationDelay: `${delay}ms`,
    ...style
  };
  
  return (
    <div ref={elementRef} className={className} style={containerStyle}>
      {children}
    </div>
  );
};

// Draggable Component with Cultural Feedback
export interface DraggableProps {
  /**
   * Content to make draggable
   */
  children: React.ReactNode;
  
  /**
   * Whether element is draggable
   * @default true
   */
  disabled?: boolean;
  
  /**
   * Drag feedback style
   * @default 'cultural'
   */
  feedbackStyle?: 'default' | 'cultural' | 'geometric' | 'minimal';
  
  /**
   * Constraint boundaries
   */
  bounds?: 'parent' | { left?: number; right?: number; top?: number; bottom?: number };
  
  /**
   * Snap to grid
   */
  grid?: [number, number];
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Container styles
   */
  style?: React.CSSProperties;
  
  /**
   * Callback when drag starts
   */
  onDragStart?: (event: React.DragEvent) => void;
  
  /**
   * Callback during drag
   */
  onDrag?: (event: React.DragEvent) => void;
  
  /**
   * Callback when drag ends
   */
  onDragEnd?: (event: React.DragEvent) => void;
}

/**
 * Draggable Component
 * 
 * Makes elements draggable with beautiful Syrian cultural feedback.
 * 
 * @example
 * ```tsx
 * // Cultural draggable element
 * <Draggable 
 *   feedbackStyle="cultural"
 *   bounds="parent"
 *   onDragStart={() => console.log('Drag started')}
 * >
 *   <div>Draggable content with cultural feedback</div>
 * </Draggable>
 * ```
 */
export const Draggable: React.FC<DraggableProps> = ({
  children,
  disabled = false,
  feedbackStyle = 'cultural',
  bounds,
  grid,
  className,
  style,
  onDragStart,
  onDrag,
  onDragEnd
}) => {
  const { prefersReducedMotion } = useAnimation();
  const [isDragging, setIsDragging] = React.useState(false);
  const [dragPosition, setDragPosition] = React.useState({ x: 0, y: 0 });
  const elementRef = React.useRef<HTMLDivElement>(null);
  const dragId = React.useId();
  
  const handleDragStart = (event: React.DragEvent) => {
    if (disabled) return;
    
    setIsDragging(true);
    onDragStart?.(event);
  };
  
  const handleDrag = (event: React.DragEvent) => {
    if (disabled || !isDragging) return;
    
    const rect = elementRef.current?.getBoundingClientRect();
    if (rect) {
      let x = event.clientX - rect.left;
      let y = event.clientY - rect.top;
      
      // Apply grid snapping
      if (grid) {
        x = Math.round(x / grid[0]) * grid[0];
        y = Math.round(y / grid[1]) * grid[1];
      }
      
      setDragPosition({ x, y });
    }
    
    onDrag?.(event);
  };
  
  const handleDragEnd = (event: React.DragEvent) => {
    setIsDragging(false);
    onDragEnd?.(event);
  };
  
  const getFeedbackStyle = (): React.CSSProperties => {
    if (!isDragging || prefersReducedMotion) return {};
    
    const baseStyle: React.CSSProperties = {
      transform: 'scale(1.05)',
      zIndex: 1000,
      transition: 'transform 0.2s ease',
    };
    
    switch (feedbackStyle) {
      case 'cultural':
        return {
          ...baseStyle,
          filter: 'drop-shadow(0 8px 16px rgba(0, 122, 61, 0.3))',
          background: 'linear-gradient(45deg, transparent 30%, rgba(0, 122, 61, 0.1) 50%, transparent 70%)',
        };
      case 'geometric':
        return {
          ...baseStyle,
          filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2))',
          outline: '2px dashed var(--sid-forest-400)',
        };
      case 'minimal':
        return {
          ...baseStyle,
          opacity: 0.8,
        };
      default:
        return baseStyle;
    }
  };
  
  const containerStyle: React.CSSProperties = {
    cursor: disabled ? 'default' : (isDragging ? 'grabbing' : 'grab'),
    userSelect: 'none',
    ...getFeedbackStyle(),
    ...style
  };
  
  return (
    <div
      ref={elementRef}
      className={className}
      style={containerStyle}
      draggable={!disabled}
      onDragStart={handleDragStart}
      onDrag={handleDrag}
      onDragEnd={handleDragEnd}
    >
      {children}
    </div>
  );
};

// Hover Effect Component
export interface HoverEffectProps {
  /**
   * Content to apply hover effects to
   */
  children: React.ReactNode;

  /**
   * Hover effect type
   * @default 'cultural'
   */
  effect?: 'cultural' | 'geometric' | 'glow' | 'lift';

  /**
   * Effect intensity
   * @default 'normal'
   */
  intensity?: 'subtle' | 'normal' | 'strong';

  /**
   * Additional CSS classes
   */
  className?: string;

  /**
   * Container styles
   */
  style?: React.CSSProperties;
}

/**
 * Hover Effect Component
 *
 * Adds beautiful hover effects with Syrian cultural elements.
 */
export const HoverEffect: React.FC<HoverEffectProps> = ({
  children,
  effect = 'cultural',
  intensity = 'normal',
  className,
  style
}) => {
  const { prefersReducedMotion, createTransition } = useAnimation();
  const [isHovered, setIsHovered] = React.useState(false);

  const getIntensityMultiplier = () => {
    const intensityMap = { subtle: 0.5, normal: 1, strong: 1.5 };
    return intensityMap[intensity];
  };

  const getHoverStyle = (): React.CSSProperties => {
    if (prefersReducedMotion) return {};

    const multiplier = getIntensityMultiplier();
    const transition = createTransition(['transform', 'filter', 'box-shadow'], 'fast', 'graceful');

    const baseStyle: React.CSSProperties = { transition };

    if (!isHovered) return baseStyle;

    switch (effect) {
      case 'cultural':
        return {
          ...baseStyle,
          transform: `scale(${1 + 0.05 * multiplier})`,
          filter: `drop-shadow(0 ${4 * multiplier}px ${8 * multiplier}px rgba(0, 122, 61, 0.3))`,
        };
      case 'geometric':
        return {
          ...baseStyle,
          transform: `scale(${1 + 0.03 * multiplier})`,
          boxShadow: `0 0 0 ${2 * multiplier}px var(--sid-forest-400)`,
        };
      case 'glow':
        return {
          ...baseStyle,
          filter: `drop-shadow(0 0 ${8 * multiplier}px var(--sid-wheat-400))`,
        };
      case 'lift':
        return {
          ...baseStyle,
          transform: `translateY(-${4 * multiplier}px)`,
          boxShadow: `0 ${8 * multiplier}px ${16 * multiplier}px rgba(0, 0, 0, 0.15)`,
        };
      default:
        return baseStyle;
    }
  };

  return (
    <div
      className={className}
      style={{ ...getHoverStyle(), ...style }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
    </div>
  );
};
