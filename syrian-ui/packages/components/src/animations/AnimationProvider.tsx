/**
 * Syrian Identity Animation Provider
 * 
 * Context provider for managing animation preferences and utilities across the app.
 * <PERSON>les reduced motion preferences and provides animation utilities.
 */

import * as React from 'react';
import { animationUtils } from '@sid/tokens';

interface AnimationContextValue {
  /**
   * Whether the user prefers reduced motion
   */
  prefersReducedMotion: boolean;
  
  /**
   * Get duration with reduced motion consideration
   */
  getDuration: (duration: string, respectReducedMotion?: boolean) => number;
  
  /**
   * Create CSS transition string
   */
  createTransition: (properties: string[], duration?: string, easing?: string) => string;
  
  /**
   * Create staggered delay for animations
   */
  createStaggerDelay: (index: number, baseDelay?: string) => number;
}

const AnimationContext = React.createContext<AnimationContextValue | null>(null);

export interface AnimationProviderProps {
  children: React.ReactNode;
  /**
   * Override reduced motion detection (useful for testing)
   */
  forceReducedMotion?: boolean;
}

/**
 * Animation Provider Component
 * 
 * Provides animation utilities and respects user motion preferences.
 * 
 * @example
 * ```tsx
 * function App() {
 *   return (
 *     <AnimationProvider>
 *       <YourComponents />
 *     </AnimationProvider>
 *   );
 * }
 * ```
 */
export const AnimationProvider: React.FC<AnimationProviderProps> = ({
  children,
  forceReducedMotion
}) => {
  const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(
    forceReducedMotion ?? false
  );

  React.useEffect(() => {
    if (forceReducedMotion !== undefined) {
      setPrefersReducedMotion(forceReducedMotion);
      return;
    }

    // Check initial preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    // Listen for changes
    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [forceReducedMotion]);

  const contextValue: AnimationContextValue = React.useMemo(() => ({
    prefersReducedMotion,
    
    getDuration: (duration: string, respectReducedMotion = true) => {
      if (respectReducedMotion && prefersReducedMotion) {
        return 0;
      }
      // Parse CSS variable or return as number
      const cssVar = `var(--sid-duration-${duration})`;
      return parseInt(duration) || 250; // fallback
    },
    
    createTransition: (properties: string[], duration = 'normal', easing = 'ease') => {
      const durationValue = prefersReducedMotion ? '0ms' : `var(--sid-duration-${duration})`;
      const easingValue = `var(--sid-ease-${easing})`;
      
      return properties
        .map(prop => `${prop} ${durationValue} ${easingValue}`)
        .join(', ');
    },
    
    createStaggerDelay: (index: number, baseDelay = 'medium') => {
      if (prefersReducedMotion) return 0;
      
      // Use CSS calc for dynamic delays
      return index * (baseDelay === 'small' ? 50 : baseDelay === 'large' ? 200 : 100);
    }
  }), [prefersReducedMotion]);

  return (
    <AnimationContext.Provider value={contextValue}>
      {children}
    </AnimationContext.Provider>
  );
};

/**
 * Hook to access animation utilities
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { prefersReducedMotion, createTransition } = useAnimation();
 *   
 *   const transition = createTransition(['opacity', 'transform'], 'fast', 'graceful');
 *   
 *   return (
 *     <div style={{ transition }}>
 *       Animated content
 *     </div>
 *   );
 * }
 * ```
 */
export const useAnimation = (): AnimationContextValue => {
  const context = React.useContext(AnimationContext);
  
  if (!context) {
    throw new Error('useAnimation must be used within an AnimationProvider');
  }
  
  return context;
};

/**
 * Hook for creating animated styles with Syrian cultural timing
 * 
 * @example
 * ```tsx
 * function CulturalComponent() {
 *   const styles = useAnimatedStyles({
 *     duration: 'contemplative',
 *     easing: 'calligraphy',
 *     properties: ['opacity', 'transform']
 *   });
 *   
 *   return <div style={styles}>Cultural content</div>;
 * }
 * ```
 */
export const useAnimatedStyles = (config: {
  duration?: string;
  easing?: string;
  properties?: string[];
  delay?: number;
}) => {
  const { createTransition, prefersReducedMotion } = useAnimation();
  
  return React.useMemo(() => {
    const {
      duration = 'normal',
      easing = 'ease',
      properties = ['all'],
      delay = 0
    } = config;
    
    const transition = createTransition(properties, duration, easing);
    
    return {
      transition,
      transitionDelay: prefersReducedMotion ? '0ms' : `${delay}ms`,
    };
  }, [config, createTransition, prefersReducedMotion]);
};

/**
 * Hook for staggered list animations
 * 
 * @example
 * ```tsx
 * function StaggeredList({ items }) {
 *   return (
 *     <div>
 *       {items.map((item, index) => {
 *         const delay = useStaggerDelay(index, 'medium');
 *         return (
 *           <div 
 *             key={item.id}
 *             style={{ 
 *               transitionDelay: `${delay}ms`,
 *               transition: 'opacity 300ms ease'
 *             }}
 *           >
 *             {item.content}
 *           </div>
 *         );
 *       })}
 *     </div>
 *   );
 * }
 * ```
 */
export const useStaggerDelay = (index: number, baseDelay = 'medium'): number => {
  const { createStaggerDelay } = useAnimation();
  return createStaggerDelay(index, baseDelay);
};
