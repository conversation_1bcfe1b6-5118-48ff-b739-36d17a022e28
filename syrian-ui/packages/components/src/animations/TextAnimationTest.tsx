/**
 * Test Component for Syrian Text Animations
 * 
 * This component tests the functionality of the advanced text animations
 * with proper Arabic text rendering and animation triggers.
 */

import * as React from 'react';
import { SyrianSplitText, CulturalTextReveal } from './AdvancedTextAnimations';
import { AnimationProvider } from './AnimationProvider';

export const TextAnimationTest: React.FC = () => {
  const [testKey, setTestKey] = React.useState(0);
  
  const resetAnimations = () => {
    setTestKey(prev => prev + 1);
  };
  
  return (
    <AnimationProvider>
      <div style={{ 
        padding: '2rem',
        backgroundColor: '#f8f9fa',
        minHeight: '100vh',
        fontFamily: 'system-ui, -apple-system, sans-serif'
      }}>
        <div style={{ marginBottom: '2rem', textAlign: 'center' }}>
          <h1 style={{ color: '#007A3D', marginBottom: '1rem' }}>
            Syrian Text Animation Test
          </h1>
          <button 
            onClick={resetAnimations}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#007A3D',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Reset Animations
          </button>
        </div>
        
        <div key={testKey} style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
          {/* Test 1: Arabic Text - Character Animation */}
          <div style={{ 
            padding: '2rem', 
            backgroundColor: 'white', 
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ marginBottom: '1rem', color: '#333' }}>
              Test 1: Arabic Character Animation
            </h3>
            <SyrianSplitText 
              text="سوريا حُرّة"
              animationStyle="cultural"
              timing="contemplative"
              splitType="chars"
              staggerDelay={100}
              direction="rtl"
              colorScheme="flag"
              variant="heading"
            />
          </div>
          
          {/* Test 2: Mixed Language - Word Animation */}
          <div style={{ 
            padding: '2rem', 
            backgroundColor: 'white', 
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ marginBottom: '1rem', color: '#333' }}>
              Test 2: Mixed Language Word Animation
            </h3>
            <SyrianSplitText 
              text="سوريا حُرّة - Free Syria"
              animationStyle="calligraphy"
              timing="ceremonial"
              splitType="words"
              staggerDelay={200}
              direction="auto"
              colorScheme="heritage"
              variant="subheading"
            />
          </div>
          
          {/* Test 3: English Text - Character Animation */}
          <div style={{ 
            padding: '2rem', 
            backgroundColor: 'white', 
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ marginBottom: '1rem', color: '#333' }}>
              Test 3: English Character Animation
            </h3>
            <SyrianSplitText 
              text="Free Syria"
              animationStyle="damascus"
              timing="contemplative"
              splitType="chars"
              staggerDelay={80}
              direction="ltr"
              colorScheme="damascus"
              variant="heading"
            />
          </div>
          
          {/* Test 4: Cultural Text Reveal - Arabic */}
          <div style={{ 
            padding: '2rem', 
            backgroundColor: 'white', 
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ marginBottom: '1rem', color: '#333' }}>
              Test 4: Cultural Text Reveal - Arabic
            </h3>
            <CulturalTextReveal 
              text="الجمهورية العربية السورية"
              pattern="bloom"
              theme="freedom"
              intensity="dramatic"
              direction="rtl"
            />
          </div>
          
          {/* Test 5: Cultural Text Reveal - Mixed */}
          <div style={{ 
            padding: '2rem', 
            backgroundColor: 'white', 
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ marginBottom: '1rem', color: '#333' }}>
              Test 5: Cultural Text Reveal - Mixed Language
            </h3>
            <CulturalTextReveal 
              text="سوريا حُرّة - Free Syria"
              pattern="damascus"
              theme="strength"
              intensity="normal"
              direction="auto"
            />
          </div>
          
          {/* Test 6: Geometric Animation */}
          <div style={{ 
            padding: '2rem', 
            backgroundColor: 'white', 
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ marginBottom: '1rem', color: '#333' }}>
              Test 6: Geometric Pattern Animation
            </h3>
            <SyrianSplitText 
              text="دمشق عاصمة الثقافة"
              animationStyle="geometric"
              timing="ceremonial"
              splitType="words"
              staggerDelay={150}
              direction="rtl"
              colorScheme="forest"
              variant="body"
            />
          </div>
          
          {/* Test 7: Performance Test - Long Text */}
          <div style={{ 
            padding: '2rem', 
            backgroundColor: 'white', 
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ marginBottom: '1rem', color: '#333' }}>
              Test 7: Performance Test - Long Text
            </h3>
            <SyrianSplitText 
              text="سوريا بلد عريق بتاريخه وحضارته العظيمة - Syria is a country with ancient history and great civilization"
              animationStyle="cultural"
              timing="fast"
              splitType="words"
              staggerDelay={50}
              direction="auto"
              colorScheme="heritage"
              variant="body"
            />
          </div>
        </div>
        
        <div style={{ 
          marginTop: '3rem', 
          padding: '1rem', 
          backgroundColor: '#e8f5e8', 
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <p style={{ margin: 0, color: '#007A3D' }}>
            <strong>Testing Notes:</strong><br/>
            • Arabic text should display with proper character joining<br/>
            • Animations should trigger when components come into view<br/>
            • No excessive whitespace should appear<br/>
            • Mixed language text should handle direction automatically<br/>
            • All animations should respect reduced motion preferences
          </p>
        </div>
      </div>
    </AnimationProvider>
  );
};

export default TextAnimationTest;
