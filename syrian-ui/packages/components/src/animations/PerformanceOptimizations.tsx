/**
 * Syrian Identity Animation Performance and Accessibility Optimizations
 * 
 * Comprehensive performance and accessibility utilities for Syrian UI animations:
 * - Reduced motion detection and handling
 * - Performance monitoring and optimization
 * - WCAG AA compliance utilities
 * - Cultural authenticity preservation
 */

import * as React from 'react';

// Performance Monitor Hook
export interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  isLowPerformance: boolean;
  memoryUsage?: number;
}

/**
 * Hook for monitoring animation performance
 * 
 * @example
 * ```tsx
 * function AnimatedComponent() {
 *   const { fps, isLowPerformance } = usePerformanceMonitor();
 *   
 *   return (
 *     <div style={{
 *       animation: isLowPerformance ? 'none' : 'myAnimation 1s ease'
 *     }}>
 *       Content (FPS: {fps})
 *     </div>
 *   );
 * }
 * ```
 */
export const usePerformanceMonitor = (): PerformanceMetrics => {
  const [metrics, setMetrics] = React.useState<PerformanceMetrics>({
    fps: 60,
    frameTime: 16.67,
    isLowPerformance: false,
  });
  
  React.useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;
    
    const measurePerformance = (currentTime: number) => {
      frameCount++;
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        const frameTime = (currentTime - lastTime) / frameCount;
        const isLowPerformance = fps < 30;
        
        setMetrics({
          fps,
          frameTime,
          isLowPerformance,
          memoryUsage: (performance as any).memory?.usedJSHeapSize,
        });
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(measurePerformance);
    };
    
    animationId = requestAnimationFrame(measurePerformance);
    
    return () => {
      cancelAnimationFrame(animationId);
    };
  }, []);
  
  return metrics;
};

// Accessibility Hook
export interface AccessibilityPreferences {
  prefersReducedMotion: boolean;
  prefersHighContrast: boolean;
  prefersReducedTransparency: boolean;
  colorScheme: 'light' | 'dark' | 'no-preference';
}

/**
 * Hook for detecting user accessibility preferences
 * 
 * @example
 * ```tsx
 * function AccessibleComponent() {
 *   const { prefersReducedMotion, prefersHighContrast } = useAccessibilityPreferences();
 *   
 *   return (
 *     <div style={{
 *       animation: prefersReducedMotion ? 'none' : 'fadeIn 0.3s ease',
 *       filter: prefersHighContrast ? 'contrast(150%)' : 'none'
 *     }}>
 *       Accessible content
 *     </div>
 *   );
 * }
 * ```
 */
export const useAccessibilityPreferences = (): AccessibilityPreferences => {
  const [preferences, setPreferences] = React.useState<AccessibilityPreferences>({
    prefersReducedMotion: false,
    prefersHighContrast: false,
    prefersReducedTransparency: false,
    colorScheme: 'no-preference',
  });
  
  React.useEffect(() => {
    const updatePreferences = () => {
      setPreferences({
        prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        prefersHighContrast: window.matchMedia('(prefers-contrast: high)').matches,
        prefersReducedTransparency: window.matchMedia('(prefers-reduced-transparency: reduce)').matches,
        colorScheme: window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' :
                    window.matchMedia('(prefers-color-scheme: light)').matches ? 'light' : 'no-preference',
      });
    };
    
    // Initial check
    updatePreferences();
    
    // Set up listeners
    const mediaQueries = [
      window.matchMedia('(prefers-reduced-motion: reduce)'),
      window.matchMedia('(prefers-contrast: high)'),
      window.matchMedia('(prefers-reduced-transparency: reduce)'),
      window.matchMedia('(prefers-color-scheme: dark)'),
      window.matchMedia('(prefers-color-scheme: light)'),
    ];
    
    mediaQueries.forEach(mq => mq.addEventListener('change', updatePreferences));
    
    return () => {
      mediaQueries.forEach(mq => mq.removeEventListener('change', updatePreferences));
    };
  }, []);
  
  return preferences;
};

// Optimized Animation Component
export interface OptimizedAnimationProps {
  /**
   * Children to animate
   */
  children: React.ReactNode;
  
  /**
   * Animation configuration
   */
  animation: {
    keyframes: string;
    duration: number;
    easing?: string;
    delay?: number;
    iterationCount?: number | 'infinite';
  };
  
  /**
   * Performance optimization level
   * @default 'balanced'
   */
  optimization?: 'performance' | 'balanced' | 'quality';
  
  /**
   * Whether to respect user preferences
   * @default true
   */
  respectPreferences?: boolean;
  
  /**
   * Fallback for reduced motion
   */
  reducedMotionFallback?: React.CSSProperties;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Container styles
   */
  style?: React.CSSProperties;
}

/**
 * Optimized Animation Component
 * 
 * High-performance animation component with accessibility and cultural preservation.
 * 
 * @example
 * ```tsx
 * // Cultural animation with optimization
 * <OptimizedAnimation
 *   animation={{
 *     keyframes: 'culturalFade',
 *     duration: 1000,
 *     easing: 'var(--sid-ease-calligraphy)'
 *   }}
 *   optimization="balanced"
 *   reducedMotionFallback={{ opacity: 1 }}
 * >
 *   <div>Optimized cultural content</div>
 * </OptimizedAnimation>
 * ```
 */
export const OptimizedAnimation: React.FC<OptimizedAnimationProps> = ({
  children,
  animation,
  optimization = 'balanced',
  respectPreferences = true,
  reducedMotionFallback,
  className,
  style
}) => {
  const { isLowPerformance } = usePerformanceMonitor();
  const { prefersReducedMotion, prefersHighContrast } = useAccessibilityPreferences();
  const elementRef = React.useRef<HTMLDivElement>(null);
  const animationId = React.useId();
  
  // Determine if animation should be disabled
  const shouldDisableAnimation = React.useMemo(() => {
    if (!respectPreferences) return false;
    
    return prefersReducedMotion || 
           (optimization === 'performance' && isLowPerformance);
  }, [prefersReducedMotion, isLowPerformance, optimization, respectPreferences]);
  
  // Inject optimized keyframes
  React.useEffect(() => {
    if (typeof document === 'undefined' || shouldDisableAnimation) return;
    
    const styleId = `sid-optimized-${animation.keyframes}-${animationId}`;
    if (document.getElementById(styleId)) return;
    
    const style = document.createElement('style');
    style.id = styleId;
    
    // Optimize keyframes based on performance level
    let optimizedKeyframes = '';
    switch (optimization) {
      case 'performance':
        // Simplified keyframes for better performance
        optimizedKeyframes = `
          @keyframes ${animation.keyframes}-${animationId} {
            0% { opacity: 0; }
            100% { opacity: 1; }
          }
        `;
        break;
        
      case 'quality':
        // Full quality keyframes
        optimizedKeyframes = `
          @keyframes ${animation.keyframes}-${animationId} {
            0% { 
              opacity: 0; 
              transform: scale(0.9) rotate(-2deg); 
              filter: blur(2px); 
            }
            50% { 
              opacity: 0.7; 
              transform: scale(1.02) rotate(1deg); 
              filter: blur(1px); 
            }
            100% { 
              opacity: 1; 
              transform: scale(1) rotate(0deg); 
              filter: blur(0px); 
            }
          }
        `;
        break;
        
      default: // balanced
        optimizedKeyframes = `
          @keyframes ${animation.keyframes}-${animationId} {
            0% { opacity: 0; transform: scale(0.95); }
            100% { opacity: 1; transform: scale(1); }
          }
        `;
        break;
    }
    
    style.textContent = optimizedKeyframes;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, [animation.keyframes, animationId, optimization, shouldDisableAnimation]);
  
  // Get optimized styles
  const getOptimizedStyles = (): React.CSSProperties => {
    const baseStyles: React.CSSProperties = {
      ...style
    };
    
    if (shouldDisableAnimation) {
      return {
        ...baseStyles,
        ...reducedMotionFallback,
      };
    }
    
    const animationString = `${animation.keyframes}-${animationId} ${animation.duration}ms ${animation.easing || 'ease'} ${animation.delay || 0}ms ${animation.iterationCount || 1}`;
    
    return {
      ...baseStyles,
      animation: animationString,
      // Performance optimizations
      willChange: optimization === 'performance' ? 'auto' : 'transform, opacity',
      backfaceVisibility: 'hidden',
      perspective: 1000,
      // Accessibility enhancements
      filter: prefersHighContrast ? 'contrast(120%)' : undefined,
    };
  };
  
  return (
    <div
      ref={elementRef}
      className={className}
      style={getOptimizedStyles()}
      // Accessibility attributes
      aria-hidden={shouldDisableAnimation ? undefined : 'false'}
      role={shouldDisableAnimation ? undefined : 'presentation'}
    >
      {children}
    </div>
  );
};

// Cultural Accessibility Utilities
export const culturalA11yUtils = {
  /**
   * Get culturally appropriate ARIA labels
   */
  getCulturalAriaLabel: (element: string, language: 'ar' | 'en' = 'en'): string => {
    const labels = {
      ar: {
        loading: 'جارٍ التحميل',
        button: 'زر',
        menu: 'قائمة',
        dialog: 'مربع حوار',
        progress: 'شريط التقدم',
        animation: 'رسوم متحركة ثقافية',
      },
      en: {
        loading: 'Loading',
        button: 'Button',
        menu: 'Menu',
        dialog: 'Dialog',
        progress: 'Progress bar',
        animation: 'Cultural animation',
      }
    };
    
    return labels[language][element as keyof typeof labels.en] || element;
  },
  
  /**
   * Check if color contrast meets WCAG AA standards
   */
  checkColorContrast: (foreground: string, background: string): boolean => {
    // Simplified contrast check - in production, use a proper color contrast library
    const getLuminance = (color: string): number => {
      // This is a simplified implementation
      // In production, use a proper color parsing library
      return 0.5; // Placeholder
    };
    
    const fgLuminance = getLuminance(foreground);
    const bgLuminance = getLuminance(background);
    const contrast = (Math.max(fgLuminance, bgLuminance) + 0.05) / (Math.min(fgLuminance, bgLuminance) + 0.05);
    
    return contrast >= 4.5; // WCAG AA standard
  },
  
  /**
   * Get culturally appropriate focus styles
   */
  getCulturalFocusStyles: (intensity: 'subtle' | 'normal' | 'strong' = 'normal'): React.CSSProperties => {
    const intensityMap = {
      subtle: {
        outline: '2px solid var(--sid-forest-400)',
        outlineOffset: '2px',
        boxShadow: '0 0 0 3px rgba(0, 122, 61, 0.2)',
      },
      normal: {
        outline: '2px solid var(--sid-forest-500)',
        outlineOffset: '2px',
        boxShadow: '0 0 0 4px rgba(0, 122, 61, 0.3)',
      },
      strong: {
        outline: '3px solid var(--sid-forest-600)',
        outlineOffset: '3px',
        boxShadow: '0 0 0 6px rgba(0, 122, 61, 0.4)',
      }
    };
    
    return intensityMap[intensity];
  }
};
