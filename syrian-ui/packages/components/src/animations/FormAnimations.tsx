/**
 * Syrian Identity Form Animation Components
 * 
 * Beautiful form animations with Syrian cultural elements and RTL support:
 * - Input focus animations with Syrian color transitions
 * - Form validation feedback with cultural-appropriate visual cues
 * - Multi-step form transitions with ceremonial timing
 * - Field reveal animations with graceful easing
 */

import * as React from 'react';
import { useAnimation } from './AnimationProvider';

// Animated Input Wrapper Component
export interface AnimatedInputProps {
  /**
   * Input element or component
   */
  children: React.ReactElement;
  
  /**
   * Label text
   */
  label?: string;
  
  /**
   * Whether input has focus
   */
  focused?: boolean;
  
  /**
   * Whether input has value
   */
  hasValue?: boolean;
  
  /**
   * Validation state
   */
  validationState?: 'valid' | 'invalid' | 'warning' | 'neutral';
  
  /**
   * Validation message
   */
  validationMessage?: string;
  
  /**
   * Animation style
   * @default 'default'
   */
  animationStyle?: 'default' | 'cultural' | 'minimal';
  
  /**
   * Direction for RTL support
   * @default 'auto'
   */
  direction?: 'ltr' | 'rtl' | 'auto';
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Container styles
   */
  style?: React.CSSProperties;
}

/**
 * Animated Input Wrapper Component
 * 
 * Enhances input fields with beautiful focus animations and validation feedback.
 * 
 * @example
 * ```tsx
 * // Cultural input animation
 * <AnimatedInput 
 *   label="الاسم الكامل"
 *   focused={isFocused}
 *   hasValue={!!value}
 *   validationState="valid"
 *   animationStyle="cultural"
 *   direction="rtl"
 * >
 *   <input type="text" value={value} onChange={handleChange} />
 * </AnimatedInput>
 * 
 * // English input with validation
 * <AnimatedInput 
 *   label="Full Name"
 *   focused={isFocused}
 *   hasValue={!!value}
 *   validationState="invalid"
 *   validationMessage="Please enter your full name"
 *   direction="ltr"
 * >
 *   <input type="text" value={value} onChange={handleChange} />
 * </AnimatedInput>
 * ```
 */
export const AnimatedInput: React.FC<AnimatedInputProps> = ({
  children,
  label,
  focused = false,
  hasValue = false,
  validationState = 'neutral',
  validationMessage,
  animationStyle = 'default',
  direction = 'auto',
  className,
  style
}) => {
  const { prefersReducedMotion, createTransition } = useAnimation();
  const inputId = React.useId();
  
  // Get validation colors
  const getValidationColor = () => {
    switch (validationState) {
      case 'valid':
        return 'var(--sid-forest-500)';
      case 'invalid':
        return '#CE1126'; // Syrian flag red
      case 'warning':
        return 'var(--sid-wheat-600)';
      default:
        return 'var(--sid-charcoal-400)';
    }
  };
  
  // Get cultural animation properties
  const getCulturalProps = () => {
    if (animationStyle !== 'cultural') return {};
    
    return {
      filter: focused ? 'drop-shadow(0 0 8px rgba(0, 122, 61, 0.3))' : 'none',
      backgroundImage: focused ? 
        'linear-gradient(45deg, transparent 30%, rgba(0, 122, 61, 0.05) 50%, transparent 70%)' : 
        'none',
    };
  };
  
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    direction: direction === 'auto' ? undefined : direction,
    ...style
  };
  
  const labelStyle: React.CSSProperties = {
    position: 'absolute',
    top: focused || hasValue ? '-8px' : '50%',
    [direction === 'rtl' ? 'right' : 'left']: '12px',
    transform: focused || hasValue ? 'translateY(0) scale(0.85)' : 'translateY(-50%) scale(1)',
    transformOrigin: direction === 'rtl' ? 'right center' : 'left center',
    color: focused ? getValidationColor() : 'var(--sid-charcoal-600)',
    fontSize: '0.875rem',
    fontWeight: focused ? 'var(--sid-font-medium)' : 'var(--sid-font-normal)',
    backgroundColor: 'white',
    padding: '0 4px',
    borderRadius: '2px',
    transition: prefersReducedMotion ? 'none' : createTransition(
      ['top', 'transform', 'color', 'font-weight'], 
      'normal', 
      animationStyle === 'cultural' ? 'calligraphy' : 'graceful'
    ),
    pointerEvents: 'none',
    zIndex: 1,
  };
  
  const inputWrapperStyle: React.CSSProperties = {
    position: 'relative',
    borderRadius: 'var(--sid-radius-input)',
    border: `2px solid ${getValidationColor()}`,
    borderColor: focused ? getValidationColor() : 'var(--sid-charcoal-300)',
    transition: prefersReducedMotion ? 'none' : createTransition(
      ['border-color', 'box-shadow', 'filter', 'background-image'], 
      'fast', 
      animationStyle === 'cultural' ? 'calligraphy' : 'ease'
    ),
    boxShadow: focused ? `0 0 0 3px ${getValidationColor()}20` : 'none',
    ...getCulturalProps(),
  };
  
  const validationStyle: React.CSSProperties = {
    marginTop: '4px',
    fontSize: '0.75rem',
    color: getValidationColor(),
    opacity: validationMessage ? 1 : 0,
    transform: validationMessage ? 'translateY(0)' : 'translateY(-8px)',
    transition: prefersReducedMotion ? 'none' : createTransition(
      ['opacity', 'transform'], 
      'normal', 
      'graceful'
    ),
  };
  
  return (
    <div className={className} style={containerStyle}>
      <div style={inputWrapperStyle}>
        {label && <label style={labelStyle}>{label}</label>}
        {React.cloneElement(children, {
          style: {
            ...children.props.style,
            width: '100%',
            padding: '12px',
            border: 'none',
            outline: 'none',
            backgroundColor: 'transparent',
            fontSize: '1rem',
            fontFamily: 'var(--sid-font-arabic)',
          }
        })}
      </div>
      {validationMessage && (
        <div style={validationStyle}>
          {validationMessage}
        </div>
      )}
    </div>
  );
};

// Multi-Step Form Component
export interface MultiStepFormProps {
  /**
   * Current step index
   */
  currentStep: number;
  
  /**
   * Total number of steps
   */
  totalSteps: number;
  
  /**
   * Step content
   */
  children: React.ReactNode[];
  
  /**
   * Step titles
   */
  stepTitles?: string[];
  
  /**
   * Transition animation
   * @default 'slideLeft'
   */
  transition?: 'slideLeft' | 'slideRight' | 'fade' | 'scale' | 'cultural';
  
  /**
   * Animation duration
   * @default 'comfortable'
   */
  duration?: 'normal' | 'comfortable' | 'slow' | 'contemplative';
  
  /**
   * Direction for RTL support
   * @default 'auto'
   */
  direction?: 'ltr' | 'rtl' | 'auto';
  
  /**
   * Show progress indicator
   * @default true
   */
  showProgress?: boolean;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Container styles
   */
  style?: React.CSSProperties;
  
  /**
   * Callback when step changes
   */
  onStepChange?: (step: number) => void;
}

/**
 * Multi-Step Form Component
 * 
 * Creates beautiful multi-step forms with cultural transitions and progress indicators.
 * 
 * @example
 * ```tsx
 * // Arabic multi-step form
 * <MultiStepForm 
 *   currentStep={currentStep}
 *   totalSteps={3}
 *   stepTitles={['المعلومات الشخصية', 'العنوان', 'التأكيد']}
 *   transition="cultural"
 *   duration="contemplative"
 *   direction="rtl"
 * >
 *   <div>Step 1 content</div>
 *   <div>Step 2 content</div>
 *   <div>Step 3 content</div>
 * </MultiStepForm>
 * ```
 */
export const MultiStepForm: React.FC<MultiStepFormProps> = ({
  currentStep,
  totalSteps,
  children,
  stepTitles,
  transition = 'slideLeft',
  duration = 'comfortable',
  direction = 'auto',
  showProgress = true,
  className,
  style,
  onStepChange
}) => {
  const { prefersReducedMotion } = useAnimation();
  const [displayStep, setDisplayStep] = React.useState(currentStep);
  const [isTransitioning, setIsTransitioning] = React.useState(false);
  const formId = React.useId();
  
  React.useEffect(() => {
    if (currentStep !== displayStep) {
      setIsTransitioning(true);
      
      const transitionDuration = prefersReducedMotion ? 0 :
        duration === 'normal' ? 250 :
        duration === 'comfortable' ? 500 :
        duration === 'slow' ? 750 : 1500;
      
      setTimeout(() => {
        setDisplayStep(currentStep);
        setIsTransitioning(false);
        onStepChange?.(currentStep);
      }, transitionDuration / 2);
    }
  }, [currentStep, displayStep, duration, prefersReducedMotion, onStepChange]);
  
  const containerStyle: React.CSSProperties = {
    direction: direction === 'auto' ? undefined : direction,
    ...style
  };
  
  const progressStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '2rem',
    gap: '1rem',
  };
  
  const stepIndicatorStyle = (stepIndex: number): React.CSSProperties => ({
    width: '32px',
    height: '32px',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '0.875rem',
    fontWeight: 'var(--sid-font-medium)',
    backgroundColor: stepIndex <= currentStep ? 'var(--sid-forest-500)' : 'var(--sid-charcoal-200)',
    color: stepIndex <= currentStep ? 'white' : 'var(--sid-charcoal-600)',
    transition: prefersReducedMotion ? 'none' : 'background-color 300ms ease, color 300ms ease',
  });
  
  const progressBarStyle: React.CSSProperties = {
    flex: 1,
    height: '2px',
    backgroundColor: 'var(--sid-charcoal-200)',
    borderRadius: '1px',
    overflow: 'hidden',
  };
  
  const progressFillStyle: React.CSSProperties = {
    height: '100%',
    backgroundColor: 'var(--sid-forest-500)',
    width: `${(currentStep / (totalSteps - 1)) * 100}%`,
    transition: prefersReducedMotion ? 'none' : 'width 500ms var(--sid-ease-graceful)',
  };
  
  const contentStyle: React.CSSProperties = {
    position: 'relative',
    overflow: 'hidden',
    minHeight: '200px',
  };
  
  const stepContentStyle: React.CSSProperties = {
    opacity: isTransitioning ? 0.5 : 1,
    transform: isTransitioning ? 'scale(0.98)' : 'scale(1)',
    transition: prefersReducedMotion ? 'none' : `opacity var(--sid-duration-${duration}) var(--sid-ease-graceful), transform var(--sid-duration-${duration}) var(--sid-ease-graceful)`,
  };
  
  return (
    <div className={className} style={containerStyle}>
      {showProgress && (
        <div style={progressStyle}>
          {Array.from({ length: totalSteps }, (_, index) => (
            <React.Fragment key={index}>
              <div style={stepIndicatorStyle(index)}>
                {index + 1}
              </div>
              {index < totalSteps - 1 && (
                <div style={progressBarStyle}>
                  <div style={progressFillStyle} />
                </div>
              )}
            </React.Fragment>
          ))}
        </div>
      )}
      
      {stepTitles && stepTitles[currentStep] && (
        <h2 style={{
          marginBottom: '1.5rem',
          color: 'var(--sid-charcoal-900)',
          fontSize: '1.5rem',
          fontWeight: 'var(--sid-font-semibold)',
        }}>
          {stepTitles[currentStep]}
        </h2>
      )}
      
      <div style={contentStyle}>
        <div style={stepContentStyle}>
          {children[displayStep]}
        </div>
      </div>
    </div>
  );
};
