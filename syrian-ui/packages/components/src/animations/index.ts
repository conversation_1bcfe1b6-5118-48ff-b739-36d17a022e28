/**
 * Syrian Identity Animation Components
 * 
 * A collection of beautiful animations inspired by Syrian culture and Islamic art.
 * All animations respect user motion preferences and include RTL support.
 */

// Animation Provider and Hooks
export {
  AnimationProvider,
  useAnimation,
  useAnimatedStyles,
  useStaggerDelay,
  type AnimationProviderProps
} from './AnimationProvider';

// Text Animations
export {
  Typewriter,
  GradientText,
  MorphingText,
  type TypewriterProps,
  type GradientTextProps,
  type MorphingTextProps
} from './TextAnimations';

// Advanced Text Animations
export {
  SyrianSplitText,
  CulturalTextReveal,
  type SyrianSplitTextProps,
  type CulturalTextRevealProps
} from './AdvancedTextAnimations';

// Text Animation Test Component
export { TextAnimationTest } from './TextAnimationTest';

// Background Animations
export {
  AnimatedGeometricPattern,
  ParticleSystem,
  AnimatedGradient,
  type AnimatedGeometricPatternProps,
  type ParticleSystemProps,
  type AnimatedGradientProps
} from './BackgroundAnimations';

// Layout Animations
export {
  CardReveal,
  StaggeredList,
  PageTransition,
  type CardRevealProps,
  type StaggeredListProps,
  type PageTransitionProps
} from './LayoutAnimations';

// Form Animations
export {
  AnimatedInput,
  MultiStepForm,
  type AnimatedInputProps,
  type MultiStepFormProps
} from './FormAnimations';

// Cultural Icon Animations
export {
  AnimatedDamascusRose,
  AnimatedOliveBranch,
  AnimatedWheatGrain,
  type AnimatedDamascusRoseProps,
  type AnimatedOliveBranchProps,
  type AnimatedWheatGrainProps
} from './CulturalIconAnimations';

// Loading Animations
export {
  GeometricSpinner,
  ProgressBar,
  SkeletonLoader,
  type GeometricSpinnerProps,
  type ProgressBarProps,
  type SkeletonLoaderProps
} from './LoadingAnimations';

// Interaction Animations
export {
  ScrollTriggered,
  Draggable,
  HoverEffect,
  type ScrollTriggeredProps,
  type DraggableProps,
  type HoverEffectProps
} from './InteractionAnimations';

// Performance and Accessibility
export {
  usePerformanceMonitor,
  useAccessibilityPreferences,
  OptimizedAnimation,
  culturalA11yUtils,
  type PerformanceMetrics,
  type AccessibilityPreferences,
  type OptimizedAnimationProps
} from './PerformanceOptimizations';

// Re-export animation tokens for convenience
export {
  sidDurations,
  sidEasing,
  sidDelays,
  sidSprings,
  sidKeyframes,
  sidAnimations,
  sidAnimationTokens,
  animationUtils
} from '@sid/tokens';
