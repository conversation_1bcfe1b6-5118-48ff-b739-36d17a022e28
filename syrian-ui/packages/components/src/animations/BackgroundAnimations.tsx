/**
 * Syrian Identity Background Animation Components
 * 
 * Beautiful animated backgrounds inspired by Syrian culture and Islamic art:
 * - Geometric pattern animations based on Islamic art
 * - Particle systems using Syrian flag colors
 * - Gradient animations with cultural color transitions
 * - Damascus-inspired pattern morphing
 */

import * as React from 'react';
import { useAnimation } from './AnimationProvider';

// Animated Geometric Pattern Background
export interface AnimatedGeometricPatternProps {
  /**
   * Pattern type inspired by Islamic art
   * @default 'damascus'
   */
  pattern?: 'damascus' | 'star' | 'hexagon' | 'arabesque' | 'geometric' | 'calligraphy';
  
  /**
   * Animation speed
   * @default 'contemplative'
   */
  speed?: 'slow' | 'contemplative' | 'moderate' | 'ceremonial';
  
  /**
   * Primary pattern color
   * @default 'var(--sid-wheat-400)'
   */
  color?: string;
  
  /**
   * Secondary pattern color
   * @default 'var(--sid-forest-300)'
   */
  secondaryColor?: string;
  
  /**
   * Pattern opacity
   * @default 0.1
   */
  opacity?: number;
  
  /**
   * Pattern size
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg';
  
  /**
   * Whether pattern should be fixed position
   * @default true
   */
  fixed?: boolean;
  
  /**
   * Z-index for layering
   * @default -1
   */
  zIndex?: number;
}

/**
 * Animated Geometric Pattern Background
 * 
 * Creates beautiful animated geometric patterns inspired by Islamic art.
 * 
 * @example
 * ```tsx
 * // Damascus pattern with cultural colors
 * <AnimatedGeometricPattern 
 *   pattern="damascus"
 *   color="var(--sid-wheat-400)"
 *   secondaryColor="var(--sid-forest-300)"
 *   speed="contemplative"
 * />
 * 
 * // Star pattern with Syrian flag colors
 * <AnimatedGeometricPattern 
 *   pattern="star"
 *   color="#007A3D"
 *   secondaryColor="#CE1126"
 *   speed="ceremonial"
 * />
 * ```
 */
export const AnimatedGeometricPattern: React.FC<AnimatedGeometricPatternProps> = ({
  pattern = 'damascus',
  speed = 'contemplative',
  color = 'var(--sid-wheat-400)',
  secondaryColor = 'var(--sid-forest-300)',
  opacity = 0.1,
  size = 'md',
  fixed = true,
  zIndex = -1
}) => {
  const { prefersReducedMotion } = useAnimation();
  const patternId = React.useId();
  
  // Get pattern size
  const getPatternSize = () => {
    const sizeMap = { sm: 60, md: 100, lg: 140 };
    return sizeMap[size];
  };
  
  // Get animation duration based on speed
  const getAnimationDuration = () => {
    const speedMap = {
      slow: 'var(--sid-duration-epic)',
      contemplative: 'var(--sid-duration-contemplative)', 
      moderate: 'var(--sid-duration-deliberate)',
      ceremonial: 'var(--sid-duration-ceremonial)'
    };
    return speedMap[speed];
  };
  
  const patternSize = getPatternSize();
  const animationDuration = getAnimationDuration();
  
  // Inject pattern-specific keyframes
  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion) return;
    
    const styleId = `sid-pattern-${pattern}-${patternId}`;
    if (document.getElementById(styleId)) return;
    
    const style = document.createElement('style');
    style.id = styleId;
    
    let keyframes = '';
    switch (pattern) {
      case 'damascus':
        keyframes = `
          @keyframes damascus-${patternId} {
            0% { transform: rotate(0deg) scale(1); opacity: ${opacity}; }
            25% { transform: rotate(90deg) scale(1.1); opacity: ${opacity * 0.7}; }
            50% { transform: rotate(180deg) scale(1); opacity: ${opacity}; }
            75% { transform: rotate(270deg) scale(1.1); opacity: ${opacity * 0.7}; }
            100% { transform: rotate(360deg) scale(1); opacity: ${opacity}; }
          }
        `;
        break;
      case 'star':
        keyframes = `
          @keyframes star-${patternId} {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.2); }
            100% { transform: rotate(360deg) scale(1); }
          }
        `;
        break;
      case 'hexagon':
        keyframes = `
          @keyframes hexagon-${patternId} {
            0% { transform: translateX(0) translateY(0); }
            33% { transform: translateX(${patternSize * 0.1}px) translateY(${patternSize * 0.1}px); }
            66% { transform: translateX(-${patternSize * 0.1}px) translateY(${patternSize * 0.1}px); }
            100% { transform: translateX(0) translateY(0); }
          }
        `;
        break;
      case 'arabesque':
        keyframes = `
          @keyframes arabesque-${patternId} {
            0% { transform: scale(1) rotate(0deg); opacity: ${opacity}; }
            25% { transform: scale(1.05) rotate(5deg); opacity: ${opacity * 1.2}; }
            50% { transform: scale(0.95) rotate(0deg); opacity: ${opacity * 0.8}; }
            75% { transform: scale(1.05) rotate(-5deg); opacity: ${opacity * 1.2}; }
            100% { transform: scale(1) rotate(0deg); opacity: ${opacity}; }
          }
        `;
        break;
      case 'geometric':
        keyframes = `
          @keyframes geometric-${patternId} {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        break;
      case 'calligraphy':
        keyframes = `
          @keyframes calligraphy-${patternId} {
            0% { transform: scaleX(1) scaleY(1); opacity: ${opacity}; }
            50% { transform: scaleX(1.1) scaleY(0.9); opacity: ${opacity * 1.3}; }
            100% { transform: scaleX(1) scaleY(1); opacity: ${opacity}; }
          }
        `;
        break;
    }
    
    style.textContent = keyframes;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, [pattern, patternId, opacity, patternSize, prefersReducedMotion]);
  
  // Generate SVG pattern
  const generatePattern = () => {
    switch (pattern) {
      case 'damascus':
        return (
          <pattern id={`damascus-${patternId}`} x="0" y="0" width={patternSize} height={patternSize} patternUnits="userSpaceOnUse">
            <circle cx={patternSize/4} cy={patternSize/4} r="4" fill={color} opacity={opacity} />
            <circle cx={3*patternSize/4} cy={3*patternSize/4} r="4" fill={color} opacity={opacity} />
            <path 
              d={`M0,${patternSize/2} Q${patternSize/4},${patternSize/4} ${patternSize/2},${patternSize/2} Q${3*patternSize/4},${3*patternSize/4} ${patternSize},${patternSize/2}`}
              stroke={secondaryColor} 
              strokeWidth="2" 
              fill="none" 
              opacity={opacity * 0.8}
            />
            <path 
              d={`M${patternSize/2},0 Q${3*patternSize/4},${patternSize/4} ${patternSize/2},${patternSize/2} Q${patternSize/4},${3*patternSize/4} ${patternSize/2},${patternSize}`}
              stroke={color} 
              strokeWidth="1" 
              fill="none" 
              opacity={opacity * 0.6}
            />
          </pattern>
        );
        
      case 'star':
        return (
          <pattern id={`star-${patternId}`} x="0" y="0" width={patternSize} height={patternSize} patternUnits="userSpaceOnUse">
            <polygon 
              points={`${patternSize/2},8 ${patternSize/2+8},${patternSize/2-8} ${patternSize-8},${patternSize/2} ${patternSize/2+8},${patternSize/2+8} ${patternSize/2},${patternSize-8} ${patternSize/2-8},${patternSize/2+8} 8,${patternSize/2} ${patternSize/2-8},${patternSize/2-8}`}
              fill={color} 
              opacity={opacity}
            />
            <circle cx={patternSize/2} cy={patternSize/2} r="3" fill={secondaryColor} opacity={opacity * 1.2} />
          </pattern>
        );
        
      case 'hexagon':
        return (
          <pattern id={`hexagon-${patternId}`} x="0" y="0" width={patternSize} height={patternSize} patternUnits="userSpaceOnUse">
            <polygon 
              points={`${patternSize/2},8 ${patternSize-12},${patternSize/4} ${patternSize-12},${3*patternSize/4} ${patternSize/2},${patternSize-8} 12,${3*patternSize/4} 12,${patternSize/4}`}
              stroke={color} 
              strokeWidth="2" 
              fill="none" 
              opacity={opacity}
            />
            <polygon 
              points={`${patternSize/2},16 ${patternSize-20},${patternSize/3} ${patternSize-20},${2*patternSize/3} ${patternSize/2},${patternSize-16} 20,${2*patternSize/3} 20,${patternSize/3}`}
              stroke={secondaryColor} 
              strokeWidth="1" 
              fill="none" 
              opacity={opacity * 0.7}
            />
          </pattern>
        );
        
      case 'arabesque':
        return (
          <pattern id={`arabesque-${patternId}`} x="0" y="0" width={patternSize} height={patternSize} patternUnits="userSpaceOnUse">
            <path 
              d={`M0,0 Q${patternSize/3},${patternSize/3} ${patternSize/2},0 Q${2*patternSize/3},${patternSize/3} ${patternSize},0 L${patternSize},${patternSize/2} Q${2*patternSize/3},${2*patternSize/3} ${patternSize/2},${patternSize/2} Q${patternSize/3},${2*patternSize/3} 0,${patternSize/2} Z`}
              fill={color} 
              opacity={opacity * 0.6}
            />
            <path 
              d={`M0,${patternSize/2} Q${patternSize/3},${patternSize/6} ${patternSize/2},${patternSize/2} Q${2*patternSize/3},${5*patternSize/6} ${patternSize},${patternSize/2} L${patternSize},${patternSize} Q${2*patternSize/3},${2*patternSize/3} ${patternSize/2},${patternSize} Q${patternSize/3},${2*patternSize/3} 0,${patternSize} Z`}
              fill={secondaryColor} 
              opacity={opacity * 0.4}
            />
          </pattern>
        );
        
      case 'geometric':
        return (
          <pattern id={`geometric-${patternId}`} x="0" y="0" width={patternSize} height={patternSize} patternUnits="userSpaceOnUse">
            <rect x="0" y="0" width={patternSize/2} height={patternSize/2} fill={color} opacity={opacity * 0.4} />
            <rect x={patternSize/2} y={patternSize/2} width={patternSize/2} height={patternSize/2} fill={color} opacity={opacity * 0.4} />
            <circle cx={patternSize/4} cy={patternSize/4} r="4" fill={secondaryColor} opacity={opacity} />
            <circle cx={3*patternSize/4} cy={3*patternSize/4} r="4" fill={secondaryColor} opacity={opacity} />
            <line x1="0" y1={patternSize/2} x2={patternSize} y2={patternSize/2} stroke={color} strokeWidth="1" opacity={opacity * 0.6} />
            <line x1={patternSize/2} y1="0" x2={patternSize/2} y2={patternSize} stroke={color} strokeWidth="1" opacity={opacity * 0.6} />
          </pattern>
        );
        
      case 'calligraphy':
        return (
          <pattern id={`calligraphy-${patternId}`} x="0" y="0" width={patternSize} height={patternSize} patternUnits="userSpaceOnUse">
            <path 
              d={`M10,${patternSize/2} Q${patternSize/4},${patternSize/4} ${patternSize/2},${patternSize/2} Q${3*patternSize/4},${3*patternSize/4} ${patternSize-10},${patternSize/2}`}
              stroke={color} 
              strokeWidth="3" 
              fill="none" 
              opacity={opacity}
              strokeLinecap="round"
            />
            <path 
              d={`M${patternSize/2},10 Q${3*patternSize/4},${patternSize/4} ${patternSize/2},${patternSize/2} Q${patternSize/4},${3*patternSize/4} ${patternSize/2},${patternSize-10}`}
              stroke={secondaryColor} 
              strokeWidth="2" 
              fill="none" 
              opacity={opacity * 0.7}
              strokeLinecap="round"
            />
          </pattern>
        );
        
      default:
        return null;
    }
  };
  
  const containerStyle: React.CSSProperties = {
    position: fixed ? 'fixed' : 'absolute',
    inset: 0,
    pointerEvents: 'none',
    zIndex,
    overflow: 'hidden',
  };
  
  const svgStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    animation: prefersReducedMotion ? 'none' : `${pattern}-${patternId} ${animationDuration} var(--sid-ease-contemplative) infinite`,
  };
  
  return (
    <div style={containerStyle}>
      <svg style={svgStyle}>
        <defs>
          {generatePattern()}
        </defs>
        <rect width="100%" height="100%" fill={`url(#${pattern}-${patternId})`} />
      </svg>
    </div>
  );
};

// Particle System Background
export interface ParticleSystemProps {
  /**
   * Number of particles
   * @default 50
   */
  count?: number;

  /**
   * Particle colors (Syrian cultural colors)
   * @default Syrian flag colors
   */
  colors?: string[];

  /**
   * Particle size range
   * @default [2, 6]
   */
  sizeRange?: [number, number];

  /**
   * Animation speed
   * @default 'contemplative'
   */
  speed?: 'slow' | 'contemplative' | 'moderate' | 'ceremonial';

  /**
   * Particle opacity
   * @default 0.6
   */
  opacity?: number;

  /**
   * Movement pattern
   * @default 'float'
   */
  movement?: 'float' | 'spiral' | 'wave' | 'cultural';

  /**
   * Whether particles should be fixed position
   * @default true
   */
  fixed?: boolean;

  /**
   * Z-index for layering
   * @default -1
   */
  zIndex?: number;
}

/**
 * Particle System Background
 *
 * Creates beautiful floating particles using Syrian cultural colors.
 *
 * @example
 * ```tsx
 * // Syrian flag colored particles
 * <ParticleSystem
 *   colors={['#007A3D', '#FFFFFF', '#CE1126']}
 *   count={30}
 *   movement="cultural"
 * />
 *
 * // Cultural palette particles
 * <ParticleSystem
 *   colors={['var(--sid-forest-400)', 'var(--sid-wheat-400)', 'var(--sid-umber-400)']}
 *   movement="spiral"
 *   speed="contemplative"
 * />
 * ```
 */
export const ParticleSystem: React.FC<ParticleSystemProps> = ({
  count = 50,
  colors = ['#007A3D', '#FFFFFF', '#CE1126', 'var(--sid-wheat-400)'],
  sizeRange = [2, 6],
  speed = 'contemplative',
  opacity = 0.6,
  movement = 'float',
  fixed = true,
  zIndex = -1
}) => {
  const { prefersReducedMotion } = useAnimation();
  const particleId = React.useId();
  const [particles, setParticles] = React.useState<Array<{
    id: string;
    x: number;
    y: number;
    size: number;
    color: string;
    delay: number;
    duration: number;
  }>>([]);

  // Generate particles
  React.useEffect(() => {
    const newParticles = Array.from({ length: count }, (_, i) => ({
      id: `particle-${i}`,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * (sizeRange[1] - sizeRange[0]) + sizeRange[0],
      color: colors[Math.floor(Math.random() * colors.length)],
      delay: Math.random() * 5000,
      duration: 3000 + Math.random() * 7000, // 3-10 seconds
    }));
    setParticles(newParticles);
  }, [count, colors, sizeRange]);

  // Inject particle animations
  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion) return;

    const styleId = `sid-particles-${movement}-${particleId}`;
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;

    let keyframes = '';
    switch (movement) {
      case 'float':
        keyframes = `
          @keyframes float-${particleId} {
            0% { transform: translateY(0px) translateX(0px); }
            25% { transform: translateY(-20px) translateX(10px); }
            50% { transform: translateY(-10px) translateX(-5px); }
            75% { transform: translateY(-30px) translateX(15px); }
            100% { transform: translateY(0px) translateX(0px); }
          }
        `;
        break;
      case 'spiral':
        keyframes = `
          @keyframes spiral-${particleId} {
            0% { transform: rotate(0deg) translateX(20px) rotate(0deg); }
            100% { transform: rotate(360deg) translateX(20px) rotate(-360deg); }
          }
        `;
        break;
      case 'wave':
        keyframes = `
          @keyframes wave-${particleId} {
            0% { transform: translateY(0px) translateX(0px); }
            25% { transform: translateY(-15px) translateX(20px); }
            50% { transform: translateY(0px) translateX(40px); }
            75% { transform: translateY(15px) translateX(20px); }
            100% { transform: translateY(0px) translateX(0px); }
          }
        `;
        break;
      case 'cultural':
        keyframes = `
          @keyframes cultural-${particleId} {
            0% { transform: scale(1) rotate(0deg) translateY(0px); opacity: ${opacity}; }
            25% { transform: scale(1.2) rotate(90deg) translateY(-10px); opacity: ${opacity * 1.3}; }
            50% { transform: scale(0.8) rotate(180deg) translateY(-5px); opacity: ${opacity * 0.7}; }
            75% { transform: scale(1.1) rotate(270deg) translateY(-15px); opacity: ${opacity * 1.1}; }
            100% { transform: scale(1) rotate(360deg) translateY(0px); opacity: ${opacity}; }
          }
        `;
        break;
    }

    style.textContent = keyframes;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [movement, particleId, opacity, prefersReducedMotion]);

  const containerStyle: React.CSSProperties = {
    position: fixed ? 'fixed' : 'absolute',
    inset: 0,
    pointerEvents: 'none',
    zIndex,
    overflow: 'hidden',
  };

  const getAnimationDuration = () => {
    const speedMap = {
      slow: '15s',
      contemplative: '12s',
      moderate: '8s',
      ceremonial: '10s'
    };
    return speedMap[speed];
  };

  return (
    <div style={containerStyle}>
      {particles.map((particle) => (
        <div
          key={particle.id}
          style={{
            position: 'absolute',
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
            borderRadius: '50%',
            opacity: prefersReducedMotion ? opacity * 0.5 : opacity,
            animation: prefersReducedMotion
              ? 'none'
              : `${movement}-${particleId} ${getAnimationDuration()} ease-in-out infinite`,
            animationDelay: `${particle.delay}ms`,
          }}
        />
      ))}
    </div>
  );
};

// Animated Gradient Background
export interface AnimatedGradientProps {
  /**
   * Gradient colors (Syrian cultural palette)
   * @default Cultural color progression
   */
  colors?: string[];

  /**
   * Gradient direction
   * @default 'diagonal'
   */
  direction?: 'horizontal' | 'vertical' | 'diagonal' | 'radial' | 'cultural';

  /**
   * Animation speed
   * @default 'contemplative'
   */
  speed?: 'slow' | 'contemplative' | 'moderate' | 'ceremonial';

  /**
   * Gradient size for animation
   * @default '400%'
   */
  size?: string;

  /**
   * Whether gradient should be fixed position
   * @default true
   */
  fixed?: boolean;

  /**
   * Z-index for layering
   * @default -1
   */
  zIndex?: number;

  /**
   * Overlay opacity
   * @default 0.8
   */
  opacity?: number;
}

/**
 * Animated Gradient Background
 *
 * Creates beautiful animated gradients using Syrian cultural colors.
 *
 * @example
 * ```tsx
 * // Syrian flag gradient
 * <AnimatedGradient
 *   colors={['#007A3D', '#FFFFFF', '#CE1126']}
 *   direction="diagonal"
 *   speed="ceremonial"
 * />
 *
 * // Cultural palette gradient
 * <AnimatedGradient
 *   colors={[
 *     'var(--sid-forest-500)',
 *     'var(--sid-wheat-400)',
 *     'var(--sid-umber-500)',
 *     'var(--sid-charcoal-600)'
 *   ]}
 *   direction="cultural"
 *   speed="contemplative"
 * />
 * ```
 */
export const AnimatedGradient: React.FC<AnimatedGradientProps> = ({
  colors = [
    'var(--sid-forest-500)',
    'var(--sid-wheat-400)',
    'var(--sid-umber-500)',
    'var(--sid-charcoal-600)',
    'var(--sid-forest-300)'
  ],
  direction = 'diagonal',
  speed = 'contemplative',
  size = '400%',
  fixed = true,
  zIndex = -1,
  opacity = 0.8
}) => {
  const { prefersReducedMotion } = useAnimation();
  const gradientId = React.useId();

  // Get gradient direction
  const getGradientDirection = () => {
    switch (direction) {
      case 'horizontal':
        return 'to right';
      case 'vertical':
        return 'to bottom';
      case 'diagonal':
        return 'to bottom right';
      case 'radial':
        return 'radial-gradient(circle';
      case 'cultural':
        return 'conic-gradient(from 0deg';
      default:
        return 'to bottom right';
    }
  };

  // Get animation duration
  const getAnimationDuration = () => {
    const speedMap = {
      slow: 'var(--sid-duration-epic)',
      contemplative: 'var(--sid-duration-contemplative)',
      moderate: 'var(--sid-duration-deliberate)',
      ceremonial: 'var(--sid-duration-ceremonial)'
    };
    return speedMap[speed];
  };

  // Inject gradient animation keyframes
  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion) return;

    const styleId = `sid-gradient-${direction}-${gradientId}`;
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;

    let keyframes = '';
    switch (direction) {
      case 'horizontal':
      case 'vertical':
      case 'diagonal':
        keyframes = `
          @keyframes gradient-${gradientId} {
            0% { background-position: 0% 50%; }
            25% { background-position: 50% 0%; }
            50% { background-position: 100% 50%; }
            75% { background-position: 50% 100%; }
            100% { background-position: 0% 50%; }
          }
        `;
        break;
      case 'radial':
        keyframes = `
          @keyframes radial-${gradientId} {
            0% { background-size: 100% 100%; }
            50% { background-size: 200% 200%; }
            100% { background-size: 100% 100%; }
          }
        `;
        break;
      case 'cultural':
        keyframes = `
          @keyframes cultural-${gradientId} {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        break;
    }

    style.textContent = keyframes;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [direction, gradientId, prefersReducedMotion]);

  // Create gradient string
  const createGradient = () => {
    const colorString = colors.join(', ');

    switch (direction) {
      case 'radial':
        return `radial-gradient(circle, ${colorString})`;
      case 'cultural':
        return `conic-gradient(from 0deg, ${colorString})`;
      default:
        return `linear-gradient(${getGradientDirection()}, ${colorString})`;
    }
  };

  const containerStyle: React.CSSProperties = {
    position: fixed ? 'fixed' : 'absolute',
    inset: 0,
    pointerEvents: 'none',
    zIndex,
    background: createGradient(),
    backgroundSize: direction === 'radial' ? '100% 100%' : size,
    opacity,
    animation: prefersReducedMotion
      ? 'none'
      : `${direction === 'radial' ? 'radial' : direction === 'cultural' ? 'cultural' : 'gradient'}-${gradientId} ${getAnimationDuration()} var(--sid-ease-contemplative) infinite`,
  };

  return <div style={containerStyle} />;
};
