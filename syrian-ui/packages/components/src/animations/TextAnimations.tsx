/**
 * Syrian Identity Text Animation Components
 * 
 * Beautiful text animations inspired by Arabic calligraphy and Syrian cultural elements.
 * Includes typewriter effects, gradient animations, and morphing text with RTL support.
 */

import * as React from 'react';
import { useAnimation } from './AnimationProvider';

/**
 * Component to inject gradient keyframes
 */
const GradientKeyframes: React.FC<{ gradientId: string }> = ({ gradientId }) => {
  React.useEffect(() => {
    if (typeof document === 'undefined') return;

    const styleId = `sid-gradient-${gradientId}`;
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      @keyframes gradient-${gradientId} {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }
    `;
    document.head.appendChild(style);
  }, [gradientId]);

  return null;
};

// Typewriter Animation Component
export interface TypewriterProps {
  /**
   * Text to animate (supports Arabic and English)
   */
  text: string;
  
  /**
   * Speed of typing in milliseconds per character
   * @default 100
   */
  speed?: number;
  
  /**
   * Delay before starting animation
   * @default 0
   */
  delay?: number;
  
  /**
   * Whether to show cursor
   * @default true
   */
  showCursor?: boolean;
  
  /**
   * Cursor character
   * @default '|'
   */
  cursor?: string;
  
  /**
   * Whether to loop the animation
   * @default false
   */
  loop?: boolean;
  
  /**
   * Callback when animation completes
   */
  onComplete?: () => void;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
}

/**
 * Typewriter Animation Component
 * 
 * Creates a typewriter effect with cultural timing and RTL support.
 * 
 * @example
 * ```tsx
 * // Arabic text with cultural timing
 * <Typewriter 
 *   text="مرحباً بكم في سوريا الجميلة"
 *   speed={120}
 *   showCursor={true}
 * />
 * 
 * // English text with faster speed
 * <Typewriter 
 *   text="Welcome to Beautiful Syria"
 *   speed={80}
 *   loop={true}
 * />
 * ```
 */
export const Typewriter: React.FC<TypewriterProps> = ({
  text,
  speed = 100,
  delay = 0,
  showCursor = true,
  cursor = '|',
  loop = false,
  onComplete,
  className,
  style
}) => {
  const [displayText, setDisplayText] = React.useState('');
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const [isComplete, setIsComplete] = React.useState(false);
  const { prefersReducedMotion } = useAnimation();
  
  React.useEffect(() => {
    if (prefersReducedMotion) {
      setDisplayText(text);
      setIsComplete(true);
      onComplete?.();
      return;
    }
    
    const startTimeout = setTimeout(() => {
      const interval = setInterval(() => {
        setCurrentIndex(prev => {
          if (prev >= text.length) {
            setIsComplete(true);
            onComplete?.();
            clearInterval(interval);
            
            if (loop) {
              setTimeout(() => {
                setCurrentIndex(0);
                setDisplayText('');
                setIsComplete(false);
              }, 1000);
            }
            
            return prev;
          }
          
          setDisplayText(text.slice(0, prev + 1));
          return prev + 1;
        });
      }, speed);
      
      return () => clearInterval(interval);
    }, delay);
    
    return () => clearTimeout(startTimeout);
  }, [text, speed, delay, loop, onComplete, prefersReducedMotion]);
  
  // Inject blink animation
  React.useEffect(() => {
    if (typeof document === 'undefined') return;

    const styleId = 'sid-typewriter-blink';
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
      }
    `;
    document.head.appendChild(style);
  }, []);

  const cursorStyle: React.CSSProperties = {
    opacity: showCursor && !isComplete ? 1 : 0,
    animation: showCursor && !isComplete ? 'blink 1s infinite' : 'none',
  };
  
  return (
    <span className={className} style={style}>
      {displayText}
      {showCursor && (
        <span style={cursorStyle}>
          {cursor}
        </span>
      )}

    </span>
  );
};

// Gradient Text Animation Component
export interface GradientTextProps {
  /**
   * Text content
   */
  children: React.ReactNode;
  
  /**
   * Gradient colors (defaults to Syrian cultural colors)
   */
  colors?: string[];
  
  /**
   * Animation duration
   * @default 'ceremonial'
   */
  duration?: string;
  
  /**
   * Animation direction
   * @default 'horizontal'
   */
  direction?: 'horizontal' | 'vertical' | 'diagonal';
  
  /**
   * Whether to animate continuously
   * @default true
   */
  animate?: boolean;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
}

/**
 * Gradient Text Animation Component
 * 
 * Creates beautiful gradient text animations using Syrian cultural colors.
 * 
 * @example
 * ```tsx
 * // Syrian flag colors gradient
 * <GradientText colors={['#007A3D', '#FFFFFF', '#CE1126']}>
 *   النص المتدرج بألوان العلم السوري
 * </GradientText>
 * 
 * // Cultural palette gradient
 * <GradientText 
 *   colors={['var(--sid-forest-500)', 'var(--sid-wheat-400)', 'var(--sid-umber-500)']}
 *   direction="diagonal"
 * >
 *   Beautiful Syrian Heritage
 * </GradientText>
 * ```
 */
export const GradientText: React.FC<GradientTextProps> = ({
  children,
  colors = ['var(--sid-forest-500)', 'var(--sid-wheat-400)', 'var(--sid-umber-500)'],
  duration = 'ceremonial',
  direction = 'horizontal',
  animate = true,
  className,
  style
}) => {
  const { prefersReducedMotion } = useAnimation();
  const gradientId = React.useId();
  
  const getGradientDirection = () => {
    switch (direction) {
      case 'vertical':
        return 'to bottom';
      case 'diagonal':
        return 'to bottom right';
      default:
        return 'to right';
    }
  };
  
  const gradientStyle: React.CSSProperties = {
    background: `linear-gradient(${getGradientDirection()}, ${colors.join(', ')})`,
    backgroundSize: animate && !prefersReducedMotion ? '200% 200%' : '100% 100%',
    backgroundClip: 'text',
    WebkitBackgroundClip: 'text',
    color: 'transparent',
    animation: animate && !prefersReducedMotion 
      ? `gradient-${gradientId} var(--sid-duration-${duration}) var(--sid-ease-melodic) infinite` 
      : 'none',
    ...style
  };
  
  return (
    <>
      <span className={className} style={gradientStyle}>
        {children}
      </span>
      {animate && !prefersReducedMotion && (
        <GradientKeyframes gradientId={gradientId} />
      )}
    </>
  );
};

// Morphing Text Animation Component
export interface MorphingTextProps {
  /**
   * Array of texts to morph between
   */
  texts: string[];
  
  /**
   * Duration for each text display
   * @default 'contemplative'
   */
  displayDuration?: string;
  
  /**
   * Duration for morphing transition
   * @default 'comfortable'
   */
  morphDuration?: string;
  
  /**
   * Whether to loop through texts
   * @default true
   */
  loop?: boolean;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
}

/**
 * Morphing Text Animation Component
 * 
 * Smoothly transitions between different texts with cultural timing.
 * 
 * @example
 * ```tsx
 * // Arabic and English mixed
 * <MorphingText 
 *   texts={[
 *     'مرحباً بكم',
 *     'Welcome',
 *     'أهلاً وسهلاً',
 *     'Bienvenue'
 *   ]}
 *   displayDuration="contemplative"
 *   morphDuration="graceful"
 * />
 * ```
 */
export const MorphingText: React.FC<MorphingTextProps> = ({
  texts,
  displayDuration = 'contemplative',
  morphDuration = 'comfortable',
  loop = true,
  className,
  style
}) => {
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const [isTransitioning, setIsTransitioning] = React.useState(false);
  const { prefersReducedMotion, getDuration } = useAnimation();
  
  React.useEffect(() => {
    if (prefersReducedMotion || texts.length <= 1) return;
    
    const displayTime = getDuration(displayDuration);
    const morphTime = getDuration(morphDuration);
    
    const interval = setInterval(() => {
      setIsTransitioning(true);
      
      setTimeout(() => {
        setCurrentIndex(prev => {
          const next = prev + 1;
          if (next >= texts.length) {
            return loop ? 0 : prev;
          }
          return next;
        });
        setIsTransitioning(false);
      }, morphTime / 2);
      
    }, displayTime + morphTime);
    
    return () => clearInterval(interval);
  }, [texts, displayDuration, morphDuration, loop, prefersReducedMotion, getDuration]);
  
  const textStyle: React.CSSProperties = {
    opacity: isTransitioning ? 0 : 1,
    transform: isTransitioning ? 'translateY(10px)' : 'translateY(0)',
    transition: `opacity var(--sid-duration-${morphDuration}) var(--sid-ease-graceful), 
                 transform var(--sid-duration-${morphDuration}) var(--sid-ease-graceful)`,
    ...style
  };
  
  return (
    <span className={className} style={textStyle}>
      {texts[currentIndex] || texts[0]}
    </span>
  );
};
