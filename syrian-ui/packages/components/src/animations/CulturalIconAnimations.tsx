/**
 * Syrian Identity Cultural Icon Animations
 * 
 * Beautiful animated icons featuring Syrian cultural elements:
 * - Animated Damascus rose icon with blooming effect
 * - Olive branch growing animation with leaves
 * - Traditional pattern morphing effects
 * - Syrian star constellation animation
 * - Wheat grain swaying animation
 */

import * as React from 'react';
import { useAnimation } from './AnimationProvider';

// Base Animated Icon Props
interface BaseAnimatedIconProps {
  /**
   * Icon size
   * @default 24
   */
  size?: number;
  
  /**
   * Icon color
   * @default 'currentColor'
   */
  color?: string;
  
  /**
   * Secondary color for multi-color icons
   */
  secondaryColor?: string;
  
  /**
   * Animation speed
   * @default 'contemplative'
   */
  speed?: 'slow' | 'contemplative' | 'moderate' | 'ceremonial';
  
  /**
   * Whether animation should loop
   * @default true
   */
  loop?: boolean;
  
  /**
   * Animation delay
   * @default 0
   */
  delay?: number;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
}

// Animated Damascus Rose Icon
export interface AnimatedDamascusRoseProps extends BaseAnimatedIconProps {
  /**
   * Rose bloom stage (0-1)
   * @default 1
   */
  bloomStage?: number;
}

/**
 * Animated Damascus Rose Icon
 * 
 * Beautiful blooming rose animation representing Syrian heritage.
 * 
 * @example
 * ```tsx
 * // Blooming rose with cultural colors
 * <AnimatedDamascusRose 
 *   size={48}
 *   color="#CE1126"
 *   secondaryColor="var(--sid-forest-500)"
 *   speed="contemplative"
 * />
 * ```
 */
export const AnimatedDamascusRose: React.FC<AnimatedDamascusRoseProps> = ({
  size = 24,
  color = '#CE1126',
  secondaryColor = 'var(--sid-forest-500)',
  speed = 'contemplative',
  loop = true,
  delay = 0,
  bloomStage = 1,
  className,
  style
}) => {
  const { prefersReducedMotion } = useAnimation();
  const roseId = React.useId();
  
  // Inject rose animation keyframes
  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion) return;
    
    const styleId = `sid-rose-${roseId}`;
    if (document.getElementById(styleId)) return;
    
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      @keyframes roseBloom-${roseId} {
        0% { transform: scale(0.3) rotate(-10deg); opacity: 0.5; }
        25% { transform: scale(0.6) rotate(-5deg); opacity: 0.7; }
        50% { transform: scale(0.9) rotate(2deg); opacity: 0.9; }
        75% { transform: scale(1.1) rotate(-2deg); opacity: 1; }
        100% { transform: scale(1) rotate(0deg); opacity: 1; }
      }
      
      @keyframes petalFloat-${roseId} {
        0%, 100% { transform: translateY(0) rotate(0deg); }
        25% { transform: translateY(-2px) rotate(1deg); }
        50% { transform: translateY(-1px) rotate(-1deg); }
        75% { transform: translateY(-3px) rotate(2deg); }
      }
      
      @keyframes stemGrow-${roseId} {
        0% { stroke-dasharray: 0 100; }
        100% { stroke-dasharray: 100 0; }
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, [roseId, prefersReducedMotion]);
  
  const getAnimationDuration = () => {
    const speedMap = {
      slow: '4s',
      contemplative: '3s',
      moderate: '2s',
      ceremonial: '5s'
    };
    return speedMap[speed];
  };
  
  const iconStyle: React.CSSProperties = {
    width: size,
    height: size,
    ...style
  };
  
  return (
    <svg
      className={className}
      style={iconStyle}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Rose stem */}
      <path
        d="M12 20 L12 12"
        stroke={secondaryColor}
        strokeWidth="2"
        strokeLinecap="round"
        style={{
          animation: prefersReducedMotion ? 'none' : `stemGrow-${roseId} ${getAnimationDuration()} ease-out`,
          animationDelay: `${delay}ms`,
          animationIterationCount: loop ? 'infinite' : 1,
        }}
      />
      
      {/* Rose leaves */}
      <path
        d="M10 16 Q8 15 9 13 Q10 14 12 14"
        fill={secondaryColor}
        opacity="0.8"
        style={{
          animation: prefersReducedMotion ? 'none' : `petalFloat-${roseId} ${getAnimationDuration()} ease-in-out`,
          animationDelay: `${delay + 500}ms`,
          animationIterationCount: loop ? 'infinite' : 1,
        }}
      />
      
      {/* Rose petals - outer layer */}
      <circle
        cx="12"
        cy="8"
        r="4"
        fill={color}
        opacity="0.6"
        style={{
          transformOrigin: '12px 8px',
          animation: prefersReducedMotion ? 'none' : `roseBloom-${roseId} ${getAnimationDuration()} ease-out`,
          animationDelay: `${delay}ms`,
          animationIterationCount: loop ? 'infinite' : 1,
        }}
      />
      
      {/* Rose petals - middle layer */}
      <circle
        cx="12"
        cy="8"
        r="2.5"
        fill={color}
        opacity="0.8"
        style={{
          transformOrigin: '12px 8px',
          animation: prefersReducedMotion ? 'none' : `roseBloom-${roseId} ${getAnimationDuration()} ease-out`,
          animationDelay: `${delay + 200}ms`,
          animationIterationCount: loop ? 'infinite' : 1,
        }}
      />
      
      {/* Rose center */}
      <circle
        cx="12"
        cy="8"
        r="1"
        fill={color}
        style={{
          transformOrigin: '12px 8px',
          animation: prefersReducedMotion ? 'none' : `roseBloom-${roseId} ${getAnimationDuration()} ease-out`,
          animationDelay: `${delay + 400}ms`,
          animationIterationCount: loop ? 'infinite' : 1,
        }}
      />
    </svg>
  );
};

// Animated Olive Branch Icon
export interface AnimatedOliveBranchProps extends BaseAnimatedIconProps {}

/**
 * Animated Olive Branch Icon
 * 
 * Growing olive branch animation representing peace and Syrian agriculture.
 * 
 * @example
 * ```tsx
 * // Growing olive branch
 * <AnimatedOliveBranch 
 *   size={32}
 *   color="var(--sid-forest-600)"
 *   secondaryColor="var(--sid-forest-300)"
 *   speed="ceremonial"
 * />
 * ```
 */
export const AnimatedOliveBranch: React.FC<AnimatedOliveBranchProps> = ({
  size = 24,
  color = 'var(--sid-forest-600)',
  secondaryColor = 'var(--sid-forest-300)',
  speed = 'contemplative',
  loop = true,
  delay = 0,
  className,
  style
}) => {
  const { prefersReducedMotion } = useAnimation();
  const branchId = React.useId();
  
  // Inject olive branch animation keyframes
  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion) return;
    
    const styleId = `sid-olive-${branchId}`;
    if (document.getElementById(styleId)) return;
    
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      @keyframes branchGrow-${branchId} {
        0% { stroke-dasharray: 0 100; opacity: 0; }
        50% { stroke-dasharray: 50 50; opacity: 0.7; }
        100% { stroke-dasharray: 100 0; opacity: 1; }
      }
      
      @keyframes leafGrow-${branchId} {
        0% { transform: scale(0) rotate(-20deg); opacity: 0; }
        60% { transform: scale(0.5) rotate(-10deg); opacity: 0.5; }
        100% { transform: scale(1) rotate(0deg); opacity: 1; }
      }
      
      @keyframes leafSway-${branchId} {
        0%, 100% { transform: rotate(0deg); }
        25% { transform: rotate(2deg); }
        75% { transform: rotate(-2deg); }
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, [branchId, prefersReducedMotion]);
  
  const getAnimationDuration = () => {
    const speedMap = {
      slow: '5s',
      contemplative: '4s',
      moderate: '3s',
      ceremonial: '6s'
    };
    return speedMap[speed];
  };
  
  const iconStyle: React.CSSProperties = {
    width: size,
    height: size,
    ...style
  };
  
  return (
    <svg
      className={className}
      style={iconStyle}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Main branch */}
      <path
        d="M4 12 Q8 10 12 12 Q16 14 20 12"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        fill="none"
        style={{
          animation: prefersReducedMotion ? 'none' : `branchGrow-${branchId} ${getAnimationDuration()} ease-out`,
          animationDelay: `${delay}ms`,
          animationIterationCount: loop ? 'infinite' : 1,
        }}
      />
      
      {/* Olive leaves */}
      {[
        { x: 6, y: 10, delay: 200 },
        { x: 8, y: 14, delay: 400 },
        { x: 10, y: 9, delay: 600 },
        { x: 12, y: 15, delay: 800 },
        { x: 14, y: 10, delay: 1000 },
        { x: 16, y: 14, delay: 1200 },
        { x: 18, y: 11, delay: 1400 },
      ].map((leaf, index) => (
        <ellipse
          key={index}
          cx={leaf.x}
          cy={leaf.y}
          rx="1.5"
          ry="3"
          fill={secondaryColor}
          style={{
            transformOrigin: `${leaf.x}px ${leaf.y}px`,
            animation: prefersReducedMotion ? 'none' : 
              `leafGrow-${branchId} ${getAnimationDuration()} ease-out, leafSway-${branchId} 2s ease-in-out infinite`,
            animationDelay: `${delay + leaf.delay}ms, ${delay + leaf.delay + 2000}ms`,
            animationIterationCount: loop ? '1, infinite' : '1, 1',
          }}
        />
      ))}
      
      {/* Small olives */}
      {[
        { x: 7, y: 12, delay: 1600 },
        { x: 11, y: 13, delay: 1800 },
        { x: 15, y: 12, delay: 2000 },
      ].map((olive, index) => (
        <circle
          key={index}
          cx={olive.x}
          cy={olive.y}
          r="1"
          fill={color}
          opacity="0.8"
          style={{
            transformOrigin: `${olive.x}px ${olive.y}px`,
            animation: prefersReducedMotion ? 'none' : `leafGrow-${branchId} ${getAnimationDuration()} ease-out`,
            animationDelay: `${delay + olive.delay}ms`,
            animationIterationCount: loop ? 'infinite' : 1,
          }}
        />
      ))}
    </svg>
  );
};

// Animated Wheat Grain Icon
export interface AnimatedWheatGrainProps extends BaseAnimatedIconProps {}

/**
 * Animated Wheat Grain Icon
 *
 * Swaying wheat animation representing Syrian agriculture and abundance.
 */
export const AnimatedWheatGrain: React.FC<AnimatedWheatGrainProps> = ({
  size = 24,
  color = 'var(--sid-wheat-600)',
  secondaryColor = 'var(--sid-wheat-400)',
  speed = 'contemplative',
  loop = true,
  delay = 0,
  className,
  style
}) => {
  const { prefersReducedMotion } = useAnimation();
  const wheatId = React.useId();

  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion) return;

    const styleId = `sid-wheat-${wheatId}`;
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      @keyframes wheatSway-${wheatId} {
        0%, 100% { transform: rotate(0deg); }
        25% { transform: rotate(3deg); }
        75% { transform: rotate(-3deg); }
      }

      @keyframes wheatGrow-${wheatId} {
        0% { transform: scaleY(0); opacity: 0; }
        100% { transform: scaleY(1); opacity: 1; }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [wheatId, prefersReducedMotion]);

  const getAnimationDuration = () => {
    const speedMap = {
      slow: '4s',
      contemplative: '3s',
      moderate: '2s',
      ceremonial: '5s'
    };
    return speedMap[speed];
  };

  return (
    <svg
      className={className}
      style={{ width: size, height: size, ...style }}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 20 L12 8"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        style={{
          transformOrigin: '12px 20px',
          animation: prefersReducedMotion ? 'none' :
            `wheatGrow-${wheatId} ${getAnimationDuration()} ease-out, wheatSway-${wheatId} 2s ease-in-out infinite`,
          animationDelay: `${delay}ms, ${delay + 1000}ms`,
          animationIterationCount: loop ? '1, infinite' : '1, 1',
        }}
      />

      {[
        { x: 10, y: 8, delay: 200 },
        { x: 14, y: 9, delay: 300 },
        { x: 9, y: 10, delay: 400 },
        { x: 15, y: 11, delay: 500 },
        { x: 10, y: 12, delay: 600 },
        { x: 14, y: 13, delay: 700 },
      ].map((grain, index) => (
        <ellipse
          key={index}
          cx={grain.x}
          cy={grain.y}
          rx="1"
          ry="2"
          fill={secondaryColor || color}
          style={{
            transformOrigin: `${grain.x}px ${grain.y}px`,
            animation: prefersReducedMotion ? 'none' : `wheatGrow-${wheatId} ${getAnimationDuration()} ease-out`,
            animationDelay: `${delay + grain.delay}ms`,
            animationIterationCount: loop ? 'infinite' : 1,
          }}
        />
      ))}
    </svg>
  );
};
