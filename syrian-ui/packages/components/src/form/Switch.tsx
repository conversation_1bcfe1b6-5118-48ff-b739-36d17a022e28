/**
 * Syrian Identity Switch Component
 * 
 * A beautiful toggle switch component with Syrian cultural design:
 * - RTL-first design with proper Arabic label support
 * - Cultural color variants inspired by Syrian heritage
 * - Enhanced accessibility with proper ARIA attributes
 * - Damascus-inspired smooth animations and transitions
 * - Touch-friendly sizing for mobile devices
 */

import * as React from 'react';

export type SwitchVariant = 'default' | 'cultural' | 'success' | 'warning';
export type SwitchSize = 'sm' | 'md' | 'lg';

export interface SwitchProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /**
   * Visual style variant inspired by Syrian design.
   * @default 'default'
   */
  variant?: SwitchVariant;
  
  /**
   * Size scale for different use cases.
   * @default 'md'
   */
  size?: SwitchSize;
  
  /**
   * Label text (supports Arabic).
   */
  label?: string;
  
  /**
   * Helper text below the switch.
   */
  helperText?: string;
  
  /**
   * Text to show when switch is on.
   * @default 'تشغيل' (Arabic) or 'On' (English)
   */
  onText?: string;
  
  /**
   * Text to show when switch is off.
   * @default 'إيقاف' (Arabic) or 'Off' (English)
   */
  offText?: string;
  
  /**
   * Whether to show on/off text inside the switch.
   * @default false
   */
  showText?: boolean;
  
  /**
   * Text direction override. Usually auto-detected from content.
   * @default 'auto'
   */
  dir?: 'rtl' | 'ltr' | 'auto';
}

/**
 * Syrian Identity Switch Component
 * 
 * @example
 * ```tsx
 * // Arabic switch with cultural styling
 * <Switch 
 *   variant="cultural"
 *   label="تفعيل الإشعارات"
 *   helperText="ستتلقى إشعارات عند وصول رسائل جديدة"
 *   dir="rtl"
 * />
 * 
 * // English switch with on/off text
 * <Switch
 *   label="Dark Mode"
 *   showText
 *   onText="On"
 *   offText="Off"
 *   dir="ltr"
 * />
 * ```
 */
export const Switch = React.forwardRef<HTMLInputElement, SwitchProps>(
  (
    {
      variant = 'default',
      size = 'md',
      label,
      helperText,
      onText,
      offText,
      showText = false,
      dir = 'auto',
      className,
      disabled = false,
      checked = false,
      ...rest
    },
    ref
  ) => {
    const switchId = React.useId();
    const helperTextId = React.useId();
    
    // Default text based on direction
    const defaultOnText = dir === 'rtl' || dir === 'auto' ? 'تشغيل' : 'On';
    const defaultOffText = dir === 'rtl' || dir === 'auto' ? 'إيقاف' : 'Off';
    
    const displayOnText = onText || defaultOnText;
    const displayOffText = offText || defaultOffText;
    
    // Get switch dimensions based on size
    const getSwitchDimensions = () => {
      switch (size) {
        case 'sm':
          return { width: '36px', height: '20px', thumbSize: '16px', padding: '2px' };
        case 'lg':
          return { width: '56px', height: '32px', thumbSize: '28px', padding: '2px' };
        default:
          return { width: '44px', height: '24px', thumbSize: '20px', padding: '2px' };
      }
    };
    
    const dimensions = getSwitchDimensions();
    
    // Get switch track styles
    const getTrackStyles = (): React.CSSProperties => {
      const baseStyles: React.CSSProperties = {
        width: dimensions.width,
        height: dimensions.height,
        borderRadius: dimensions.height,
        border: 'none',
        outline: 'none',
        cursor: disabled ? 'not-allowed' : 'pointer',
        transition: 'all 200ms ease',
        position: 'relative',
        flexShrink: 0,
        opacity: disabled ? 0.6 : 1,
        appearance: 'none',
      };

      // Variant-based colors
      switch (variant) {
        case 'cultural':
          return {
            ...baseStyles,
            backgroundColor: checked ? 'var(--sid-wheat-600)' : 'var(--sid-wheat-200)',
          };
        case 'success':
          return {
            ...baseStyles,
            backgroundColor: checked ? 'var(--sid-flag-green)' : 'var(--sid-charcoal-300)',
          };
        case 'warning':
          return {
            ...baseStyles,
            backgroundColor: checked ? 'var(--sid-wheat-700)' : 'var(--sid-charcoal-300)',
          };
        default:
          return {
            ...baseStyles,
            backgroundColor: checked ? 'var(--sid-forest-600)' : 'var(--sid-charcoal-300)',
          };
      }
    };

    const getThumbStyles = (): React.CSSProperties => {
      const translateX = checked 
        ? `calc(${dimensions.width} - ${dimensions.thumbSize} - ${dimensions.padding})` 
        : dimensions.padding;
        
      return {
        position: 'absolute',
        top: dimensions.padding,
        left: dir === 'rtl' ? 'auto' : translateX,
        right: dir === 'rtl' ? translateX : 'auto',
        width: dimensions.thumbSize,
        height: dimensions.thumbSize,
        backgroundColor: 'var(--sid-charcoal-0)',
        borderRadius: '50%',
        transition: 'all 200ms ease',
        boxShadow: 'var(--sid-shadow-sm)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: size === 'sm' ? '8px' : size === 'lg' ? '12px' : '10px',
        fontWeight: 'var(--sid-font-bold)',
        color: checked 
          ? variant === 'cultural' ? 'var(--sid-wheat-700)' : 'var(--sid-forest-700)'
          : 'var(--sid-charcoal-600)',
        pointerEvents: 'none',
      };
    };

    const getLabelStyles = (): React.CSSProperties => ({
      fontFamily: dir === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
      fontSize: size === 'sm' ? 'var(--sid-text-sm)' : size === 'lg' ? 'var(--sid-text-lg)' : 'var(--sid-text-base)',
      fontWeight: 'var(--sid-font-medium)',
      color: disabled ? 'var(--sid-text-tertiary)' : 'var(--sid-text-primary)',
      cursor: disabled ? 'not-allowed' : 'pointer',
      userSelect: 'none',
    });

    const getHelperTextStyles = (): React.CSSProperties => ({
      fontFamily: dir === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
      fontSize: 'var(--sid-text-xs)',
      color: 'var(--sid-text-tertiary)',
      marginBlockStart: 'var(--sid-space-1)',
    });

    const getContainerStyles = (): React.CSSProperties => ({
      display: 'flex',
      alignItems: 'flex-start',
      gap: 'var(--sid-space-3)',
      flexDirection: dir === 'rtl' ? 'row-reverse' : 'row',
    });

    return (
      <div style={getContainerStyles()}>
        <div style={{ position: 'relative' }}>
          <input
            ref={ref}
            id={switchId}
            type="checkbox"
            role="switch"
            dir={dir}
            disabled={disabled}
            checked={checked}
            aria-checked={checked}
            aria-describedby={helperText ? helperTextId : undefined}
            style={getTrackStyles()}
            className={className}
            onFocus={(e) => {
              e.target.style.boxShadow = 'var(--sid-shadow-focus)';
              rest.onFocus?.(e);
            }}
            onBlur={(e) => {
              e.target.style.boxShadow = 'none';
              rest.onBlur?.(e);
            }}
            {...rest}
          />
          
          <div style={getThumbStyles()}>
            {showText && (
              <span style={{ fontSize: 'inherit', fontWeight: 'inherit' }}>
                {checked ? displayOnText.charAt(0) : displayOffText.charAt(0)}
              </span>
            )}
          </div>
        </div>
        
        {(label || helperText) && (
          <div style={{ flex: 1 }}>
            {label && (
              <label htmlFor={switchId} style={getLabelStyles()}>
                {label}
              </label>
            )}
            {helperText && (
              <div id={helperTextId} style={getHelperTextStyles()}>
                {helperText}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }
);

Switch.displayName = 'Switch';
