/**
 * Syrian Identity UI - Global Styles
 * 
 * RTL-first CSS architecture using logical properties for direction-agnostic styling.
 * Includes Syrian design tokens, typography, and cultural considerations.
 */

/* Import design tokens */
@import '@sid/tokens/css';

/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
}

html {
  /* Enable smooth scrolling */
  scroll-behavior: smooth;
  
  /* Improve text rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  
  /* Set base font size for rem calculations */
  font-size: 16px;
}

body {
  /* Use logical properties for RTL support */
  font-family: var(--sid-font-universal);
  font-size: var(--sid-text-base);
  font-weight: var(--sid-font-regular);
  line-height: var(--sid-leading-body);
  color: var(--sid-text-primary);
  background-color: var(--sid-bg-primary);
  
  /* Improve text rendering for Arabic */
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  font-variant-ligatures: common-ligatures contextual;
  
  /* Prevent horizontal scrolling */
  overflow-x: hidden;
}

/* Arabic text optimizations */
[lang="ar"],
[dir="rtl"] {
  font-family: var(--sid-font-arabic);
  
  /* Better Arabic text rendering */
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1, 'ccmp' 1, 'locl' 1;
  font-variant-ligatures: common-ligatures contextual;
  text-align: start; /* Use logical alignment */
}

/* English/Latin text optimizations */
[lang="en"],
[dir="ltr"] {
  font-family: var(--sid-font-latin);
  text-align: start; /* Use logical alignment */
}

/* Headings with Syrian typography scale */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--sid-font-arabic);
  font-weight: var(--sid-font-semibold);
  line-height: var(--sid-leading-heading);
  color: var(--sid-text-primary);
  margin-block-end: var(--sid-space-4);
}

h1 {
  font-size: var(--sid-text-2xl);
  font-weight: var(--sid-font-bold);
}

h2 {
  font-size: var(--sid-text-xl);
}

h3 {
  font-size: var(--sid-text-lg);
}

h4 {
  font-size: var(--sid-text-base);
}

h5 {
  font-size: var(--sid-text-sm);
}

h6 {
  font-size: var(--sid-text-xs);
  font-weight: var(--sid-font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Paragraphs and text */
p {
  margin-block-end: var(--sid-space-4);
  line-height: var(--sid-leading-body);
}

/* Links */
a {
  color: var(--sid-forest-600);
  text-decoration: none;
  transition: color 150ms ease;
}

a:hover {
  color: var(--sid-forest-700);
  text-decoration: underline;
}

a:focus-visible {
  outline: 2px solid var(--sid-border-focus);
  outline-offset: 2px;
  border-radius: var(--sid-radius-sm);
}

/* Lists with logical properties */
ul, ol {
  padding-inline-start: var(--sid-space-6);
  margin-block-end: var(--sid-space-4);
}

li {
  margin-block-end: var(--sid-space-1);
}

/* Form elements base styles */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* Button reset */
button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  font-family: inherit;
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid var(--sid-border-focus);
  outline-offset: 2px;
}

/* Remove focus outline for mouse users */
:focus:not(:focus-visible) {
  outline: none;
}

/* Utility classes using logical properties */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Text alignment utilities (logical) */
.text-start {
  text-align: start;
}

.text-end {
  text-align: end;
}

.text-center {
  text-align: center;
}

/* Spacing utilities using logical properties */
.p-0 { padding: 0; }
.p-1 { padding: var(--sid-space-1); }
.p-2 { padding: var(--sid-space-2); }
.p-3 { padding: var(--sid-space-3); }
.p-4 { padding: var(--sid-space-4); }

.px-0 { padding-inline: 0; }
.px-1 { padding-inline: var(--sid-space-1); }
.px-2 { padding-inline: var(--sid-space-2); }
.px-3 { padding-inline: var(--sid-space-3); }
.px-4 { padding-inline: var(--sid-space-4); }

.py-0 { padding-block: 0; }
.py-1 { padding-block: var(--sid-space-1); }
.py-2 { padding-block: var(--sid-space-2); }
.py-3 { padding-block: var(--sid-space-3); }
.py-4 { padding-block: var(--sid-space-4); }

.m-0 { margin: 0; }
.m-1 { margin: var(--sid-space-1); }
.m-2 { margin: var(--sid-space-2); }
.m-3 { margin: var(--sid-space-3); }
.m-4 { margin: var(--sid-space-4); }

.mx-0 { margin-inline: 0; }
.mx-1 { margin-inline: var(--sid-space-1); }
.mx-2 { margin-inline: var(--sid-space-2); }
.mx-3 { margin-inline: var(--sid-space-3); }
.mx-4 { margin-inline: var(--sid-space-4); }

.my-0 { margin-block: 0; }
.my-1 { margin-block: var(--sid-space-1); }
.my-2 { margin-block: var(--sid-space-2); }
.my-3 { margin-block: var(--sid-space-3); }
.my-4 { margin-block: var(--sid-space-4); }

/* Layout utilities */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

/* Width utilities using logical properties */
.w-full {
  inline-size: 100%;
}

.w-auto {
  inline-size: auto;
}

.h-full {
  block-size: 100%;
}

.h-auto {
  block-size: auto;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --sid-border-primary: #000000;
    --sid-text-primary: #000000;
    --sid-bg-primary: #ffffff;
  }
}

/* Print styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
  
  h2,
  h3 {
    page-break-after: avoid;
  }
}
