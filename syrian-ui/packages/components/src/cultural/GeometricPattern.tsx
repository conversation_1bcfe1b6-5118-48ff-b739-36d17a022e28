/**
 * Syrian Identity Geometric Pattern Component
 * 
 * Beautiful geometric patterns inspired by Islamic art and Syrian architecture:
 * - Traditional Islamic geometric designs
 * - Damascus-inspired patterns and motifs
 * - Scalable SVG patterns with cultural authenticity
 * - Customizable colors and opacity
 * - Perfect for backgrounds and decorative elements
 */

import * as React from 'react';

export type PatternType = 'damascus' | 'star' | 'hexagon' | 'arabesque' | 'geometric';
export type PatternSize = 'sm' | 'md' | 'lg';

export interface GeometricPatternProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Type of geometric pattern.
   * @default 'damascus'
   */
  pattern?: PatternType;
  
  /**
   * Size of the pattern elements.
   * @default 'md'
   */
  size?: PatternSize;
  
  /**
   * Primary color of the pattern.
   * @default 'var(--sid-wheat-400)'
   */
  color?: string;
  
  /**
   * Secondary color for complex patterns.
   * @default 'var(--sid-wheat-200)'
   */
  secondaryColor?: string;
  
  /**
   * Opacity of the pattern.
   * @default 0.1
   */
  opacity?: number;
  
  /**
   * Whether the pattern should be fixed (for backgrounds).
   * @default false
   */
  fixed?: boolean;
}

/**
 * Syrian Identity Geometric Pattern Component
 * 
 * @example
 * ```tsx
 * // Damascus pattern background
 * <GeometricPattern 
 *   pattern="damascus"
 *   color="var(--sid-wheat-400)"
 *   opacity={0.05}
 *   fixed
 * />
 * 
 * // Star pattern overlay
 * <GeometricPattern
 *   pattern="star"
 *   size="lg"
 *   color="var(--sid-forest-300)"
 *   opacity={0.1}
 * />
 * ```
 */
export const GeometricPattern: React.FC<GeometricPatternProps> = ({
  pattern = 'damascus',
  size = 'md',
  color = 'var(--sid-wheat-400)',
  secondaryColor = 'var(--sid-wheat-200)',
  opacity = 0.1,
  fixed = false,
  className,
  style,
  ...props
}) => {
  const patternId = React.useId();
  
  // Get pattern size
  const getPatternSize = () => {
    const sizeMap = {
      sm: 40,
      md: 60,
      lg: 80
    };
    return sizeMap[size];
  };
  
  const patternSize = getPatternSize();
  
  // Get pattern SVG based on type
  const getPatternSVG = () => {
    switch (pattern) {
      case 'damascus':
        return (
          <svg width={patternSize} height={patternSize}>
            <defs>
              <pattern id={`damascus-${patternId}`} x="0" y="0" width={patternSize} height={patternSize} patternUnits="userSpaceOnUse">
                <circle cx={patternSize/4} cy={patternSize/4} r="3" fill={color} opacity={opacity} />
                <circle cx={3*patternSize/4} cy={3*patternSize/4} r="3" fill={color} opacity={opacity} />
                <path 
                  d={`M0,${patternSize/2} Q${patternSize/4},${patternSize/4} ${patternSize/2},${patternSize/2} Q${3*patternSize/4},${3*patternSize/4} ${patternSize},${patternSize/2}`}
                  stroke={secondaryColor} 
                  strokeWidth="1" 
                  fill="none" 
                  opacity={opacity * 0.7}
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill={`url(#damascus-${patternId})`} />
          </svg>
        );
        
      case 'star':
        return (
          <svg width={patternSize} height={patternSize}>
            <defs>
              <pattern id={`star-${patternId}`} x="0" y="0" width={patternSize} height={patternSize} patternUnits="userSpaceOnUse">
                <polygon 
                  points={`${patternSize/2},5 ${patternSize/2+5},${patternSize/2-5} ${patternSize-5},${patternSize/2} ${patternSize/2+5},${patternSize/2+5} ${patternSize/2},${patternSize-5} ${patternSize/2-5},${patternSize/2+5} 5,${patternSize/2} ${patternSize/2-5},${patternSize/2-5}`}
                  fill={color} 
                  opacity={opacity}
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill={`url(#star-${patternId})`} />
          </svg>
        );
        
      case 'hexagon':
        return (
          <svg width={patternSize} height={patternSize}>
            <defs>
              <pattern id={`hexagon-${patternId}`} x="0" y="0" width={patternSize} height={patternSize} patternUnits="userSpaceOnUse">
                <polygon 
                  points={`${patternSize/2},5 ${patternSize-10},${patternSize/4} ${patternSize-10},${3*patternSize/4} ${patternSize/2},${patternSize-5} 10,${3*patternSize/4} 10,${patternSize/4}`}
                  stroke={color} 
                  strokeWidth="1" 
                  fill="none" 
                  opacity={opacity}
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill={`url(#hexagon-${patternId})`} />
          </svg>
        );
        
      case 'arabesque':
        return (
          <svg width={patternSize} height={patternSize}>
            <defs>
              <pattern id={`arabesque-${patternId}`} x="0" y="0" width={patternSize} height={patternSize} patternUnits="userSpaceOnUse">
                <path 
                  d={`M0,0 Q${patternSize/4},${patternSize/4} ${patternSize/2},0 Q${3*patternSize/4},${patternSize/4} ${patternSize},0 L${patternSize},${patternSize/2} Q${3*patternSize/4},${3*patternSize/4} ${patternSize/2},${patternSize/2} Q${patternSize/4},${3*patternSize/4} 0,${patternSize/2} Z`}
                  fill={color} 
                  opacity={opacity * 0.5}
                />
                <path 
                  d={`M0,${patternSize/2} Q${patternSize/4},${patternSize/4} ${patternSize/2},${patternSize/2} Q${3*patternSize/4},${3*patternSize/4} ${patternSize},${patternSize/2} L${patternSize},${patternSize} Q${3*patternSize/4},${3*patternSize/4} ${patternSize/2},${patternSize} Q${patternSize/4},${3*patternSize/4} 0,${patternSize} Z`}
                  fill={secondaryColor} 
                  opacity={opacity * 0.3}
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill={`url(#arabesque-${patternId})`} />
          </svg>
        );
        
      case 'geometric':
        return (
          <svg width={patternSize} height={patternSize}>
            <defs>
              <pattern id={`geometric-${patternId}`} x="0" y="0" width={patternSize} height={patternSize} patternUnits="userSpaceOnUse">
                <rect x="0" y="0" width={patternSize/2} height={patternSize/2} fill={color} opacity={opacity * 0.3} />
                <rect x={patternSize/2} y={patternSize/2} width={patternSize/2} height={patternSize/2} fill={color} opacity={opacity * 0.3} />
                <circle cx={patternSize/4} cy={patternSize/4} r="2" fill={secondaryColor} opacity={opacity} />
                <circle cx={3*patternSize/4} cy={3*patternSize/4} r="2" fill={secondaryColor} opacity={opacity} />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill={`url(#geometric-${patternId})`} />
          </svg>
        );
        
      default:
        return null;
    }
  };
  
  const containerStyles: React.CSSProperties = {
    position: fixed ? 'fixed' : 'absolute',
    inset: 0,
    pointerEvents: 'none',
    zIndex: -1,
    overflow: 'hidden',
    ...style,
  };
  
  return (
    <div 
      className={className}
      style={containerStyles}
      {...props}
    >
      {getPatternSVG()}
    </div>
  );
};

GeometricPattern.displayName = 'GeometricPattern';
