/**
 * Syrian Identity Cultural Icons
 * 
 * A collection of icons inspired by Syrian culture and heritage:
 * - Damascus rose and traditional motifs
 * - Architectural elements from Syrian buildings
 * - Cultural symbols with authentic design
 * - RTL-aware icon positioning
 * - Scalable SVG icons with proper accessibility
 */

import * as React from 'react';

export interface IconProps extends React.SVGProps<SVGSVGElement> {
  /**
   * Icon size.
   * @default 24
   */
  size?: number;
  
  /**
   * Icon color (uses currentColor by default).
   */
  color?: string;
  
  /**
   * Accessible label for screen readers.
   */
  'aria-label'?: string;
}

/**
 * Damascus Rose Icon - Symbol of Syrian beauty and resilience
 */
export const DamascusRoseIcon: React.FC<IconProps> = ({ 
  size = 24, 
  color = 'currentColor', 
  'aria-label': ariaLabel,
  ...props 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    aria-label={ariaLabel || 'Damascus Rose'}
    {...props}
  >
    <path
      d="M12 2C12 2 8 4 8 8C8 10 9 11 10 12C9 13 8 14 8 16C8 20 12 22 12 22C12 22 16 20 16 16C16 14 15 13 14 12C15 11 16 10 16 8C16 4 12 2 12 2Z"
      fill={color}
      opacity="0.8"
    />
    <path
      d="M12 6C10.5 6 10 7.5 10 8.5C10 9.5 10.5 10 11 10.5C10.5 11 10 11.5 10 12.5C10 13.5 10.5 14 12 14C13.5 14 14 13.5 14 12.5C14 11.5 13.5 11 13 10.5C13.5 10 14 9.5 14 8.5C14 7.5 13.5 6 12 6Z"
      fill={color}
    />
    <circle cx="12" cy="9" r="1.5" fill="rgba(255,255,255,0.3)" />
  </svg>
);

/**
 * Olive Branch Icon - Symbol of peace and Syrian agriculture
 */
export const OliveBranchIcon: React.FC<IconProps> = ({ 
  size = 24, 
  color = 'currentColor', 
  'aria-label': ariaLabel,
  ...props 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    aria-label={ariaLabel || 'Olive Branch'}
    {...props}
  >
    <path
      d="M3 12C3 12 5 10 8 10C11 10 13 12 16 12C19 12 21 10 21 10"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
    />
    <ellipse cx="6" cy="9" rx="1.5" ry="2" fill={color} opacity="0.7" />
    <ellipse cx="9" cy="11" rx="1.5" ry="2" fill={color} opacity="0.7" />
    <ellipse cx="12" cy="9" rx="1.5" ry="2" fill={color} opacity="0.7" />
    <ellipse cx="15" cy="11" rx="1.5" ry="2" fill={color} opacity="0.7" />
    <ellipse cx="18" cy="9" rx="1.5" ry="2" fill={color} opacity="0.7" />
  </svg>
);

/**
 * Wheat Grain Icon - Symbol of Syrian agriculture and prosperity
 */
export const WheatGrainIcon: React.FC<IconProps> = ({ 
  size = 24, 
  color = 'currentColor', 
  'aria-label': ariaLabel,
  ...props 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    aria-label={ariaLabel || 'Wheat Grain'}
    {...props}
  >
    <path
      d="M12 2L12 22"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
    />
    <path
      d="M8 4C8 4 10 5 12 4C14 5 16 4 16 4"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M7 7C7 7 9.5 8 12 7C14.5 8 17 7 17 7"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M6 10C6 10 9 11 12 10C15 11 18 10 18 10"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M7 13C7 13 9.5 14 12 13C14.5 14 17 13 17 13"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M8 16C8 16 10 17 12 16C14 17 16 16 16 16"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);

/**
 * Damascus Arch Icon - Architectural element from Syrian heritage
 */
export const DamascusArchIcon: React.FC<IconProps> = ({ 
  size = 24, 
  color = 'currentColor', 
  'aria-label': ariaLabel,
  ...props 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    aria-label={ariaLabel || 'Damascus Arch'}
    {...props}
  >
    <path
      d="M4 20L4 12C4 8 7.5 4 12 4C16.5 4 20 8 20 12L20 20"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 20L6 14C6 11 8.5 8 12 8C15.5 8 18 11 18 14L18 20"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
      opacity="0.7"
    />
    <rect x="2" y="20" width="20" height="2" fill={color} />
  </svg>
);

/**
 * Syrian Star Icon - Eight-pointed star from Islamic geometry
 */
export const SyrianStarIcon: React.FC<IconProps> = ({ 
  size = 24, 
  color = 'currentColor', 
  'aria-label': ariaLabel,
  ...props 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    aria-label={ariaLabel || 'Syrian Star'}
    {...props}
  >
    <path
      d="M12 2L13.5 8.5L20 7L15.5 12L20 17L13.5 15.5L12 22L10.5 15.5L4 17L8.5 12L4 7L10.5 8.5L12 2Z"
      fill={color}
      opacity="0.8"
    />
    <path
      d="M12 6L12.8 10.2L17 9.5L14 12L17 14.5L12.8 13.8L12 18L11.2 13.8L7 14.5L10 12L7 9.5L11.2 10.2L12 6Z"
      fill="rgba(255,255,255,0.3)"
    />
  </svg>
);

/**
 * Calligraphy Pen Icon - Symbol of Arabic writing and literature
 */
export const CalligraphyPenIcon: React.FC<IconProps> = ({ 
  size = 24, 
  color = 'currentColor', 
  'aria-label': ariaLabel,
  ...props 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    aria-label={ariaLabel || 'Calligraphy Pen'}
    {...props}
  >
    <path
      d="M3 21L9 15L15 21L21 15"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 9L20 4L16 2L9 9"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9 9L15 15"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
    />
    <circle cx="18" cy="6" r="1" fill={color} />
  </svg>
);

/**
 * Collection of all Syrian cultural icons
 */
export const SyrianIcons = {
  DamascusRose: DamascusRoseIcon,
  OliveBranch: OliveBranchIcon,
  WheatGrain: WheatGrainIcon,
  DamascusArch: DamascusArchIcon,
  SyrianStar: SyrianStarIcon,
  CalligraphyPen: CalligraphyPenIcon,
} as const;

export type SyrianIconName = keyof typeof SyrianIcons;
