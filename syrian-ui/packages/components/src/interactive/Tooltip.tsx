/**
 * Syrian Identity Tooltip Component
 * 
 * A beautiful tooltip component with Syrian cultural design:
 * - RTL-first design with proper Arabic text support
 * - Cultural color variants inspired by Syrian heritage
 * - Enhanced accessibility with proper ARIA attributes
 * - Damascus-inspired styling with smooth animations
 * - Smart positioning that respects RTL layout
 */

import * as React from 'react';

export type TooltipVariant = 'default' | 'cultural' | 'dark' | 'light';
export type TooltipPlacement = 'top' | 'bottom' | 'left' | 'right' | 'auto';
export type TooltipSize = 'sm' | 'md' | 'lg';

export interface TooltipProps {
  /**
   * Content to display in the tooltip (supports Arabic).
   */
  content: React.ReactNode;
  
  /**
   * Visual style variant inspired by Syrian design.
   * @default 'default'
   */
  variant?: TooltipVariant;
  
  /**
   * Size scale for different use cases.
   * @default 'md'
   */
  size?: TooltipSize;
  
  /**
   * Preferred placement of the tooltip.
   * @default 'auto'
   */
  placement?: TooltipPlacement;
  
  /**
   * Whether the tooltip is disabled.
   * @default false
   */
  disabled?: boolean;
  
  /**
   * Delay before showing tooltip (ms).
   * @default 500
   */
  showDelay?: number;
  
  /**
   * Delay before hiding tooltip (ms).
   * @default 0
   */
  hideDelay?: number;
  
  /**
   * Text direction override. Usually auto-detected from content.
   * @default 'auto'
   */
  dir?: 'rtl' | 'ltr' | 'auto';
  
  /**
   * Element that triggers the tooltip.
   */
  children: React.ReactElement;
}

/**
 * Syrian Identity Tooltip Component
 * 
 * @example
 * ```tsx
 * // Arabic tooltip with cultural styling
 * <Tooltip 
 *   content="هذا نص مساعد باللغة العربية"
 *   variant="cultural"
 *   dir="rtl"
 * >
 *   <Button>زر مع تلميح</Button>
 * </Tooltip>
 * 
 * // English tooltip with custom placement
 * <Tooltip
 *   content="This is a helpful tooltip"
 *   placement="top"
 *   dir="ltr"
 * >
 *   <span>Hover me</span>
 * </Tooltip>
 * ```
 */
export const Tooltip: React.FC<TooltipProps> = ({
  content,
  variant = 'default',
  size = 'md',
  placement = 'auto',
  disabled = false,
  showDelay = 500,
  hideDelay = 0,
  dir = 'auto',
  children,
}) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const [actualPlacement, setActualPlacement] = React.useState<TooltipPlacement>(placement);
  const triggerRef = React.useRef<HTMLElement>(null);
  const tooltipRef = React.useRef<HTMLDivElement>(null);
  const showTimeoutRef = React.useRef<number>();
  const hideTimeoutRef = React.useRef<number>();
  const tooltipId = React.useId();

  // Calculate optimal placement
  const calculatePlacement = React.useCallback(() => {
    if (!triggerRef.current || placement !== 'auto') {
      setActualPlacement(placement);
      return;
    }

    const rect = triggerRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // Simple auto-placement logic
    if (rect.top > viewportHeight / 2) {
      setActualPlacement('top');
    } else if (rect.left > viewportWidth / 2) {
      setActualPlacement('left');
    } else {
      setActualPlacement('bottom');
    }
  }, [placement]);

  const showTooltip = React.useCallback(() => {
    if (disabled) return;
    
    if (hideTimeoutRef.current) {
      window.clearTimeout(hideTimeoutRef.current);
    }
    
    showTimeoutRef.current = window.setTimeout(() => {
      calculatePlacement();
      setIsVisible(true);
    }, showDelay);
  }, [disabled, showDelay, calculatePlacement]);

  const hideTooltip = React.useCallback(() => {
    if (showTimeoutRef.current) {
      window.clearTimeout(showTimeoutRef.current);
    }
    
    hideTimeoutRef.current = window.setTimeout(() => {
      setIsVisible(false);
    }, hideDelay);
  }, [hideDelay]);

  // Cleanup timeouts
  React.useEffect(() => {
    return () => {
      if (showTimeoutRef.current) window.clearTimeout(showTimeoutRef.current);
      if (hideTimeoutRef.current) window.clearTimeout(hideTimeoutRef.current);
    };
  }, []);

  // Get tooltip styles
  const getTooltipStyles = (): React.CSSProperties => {
    const sizeMap = {
      sm: {
        fontSize: 'var(--sid-text-xs)',
        padding: 'var(--sid-space-1) var(--sid-space-2)',
        maxWidth: '200px'
      },
      md: {
        fontSize: 'var(--sid-text-sm)',
        padding: 'var(--sid-space-2) var(--sid-space-3)',
        maxWidth: '280px'
      },
      lg: {
        fontSize: 'var(--sid-text-base)',
        padding: 'var(--sid-space-3) var(--sid-space-4)',
        maxWidth: '320px'
      }
    };

    const baseStyles: React.CSSProperties = {
      position: 'absolute',
      zIndex: 9999,
      borderRadius: 'var(--sid-radius-md)',
      fontSize: sizeMap[size].fontSize,
      padding: sizeMap[size].padding,
      maxWidth: sizeMap[size].maxWidth,
      fontFamily: dir === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
      lineHeight: 'var(--sid-leading-snug)',
      textAlign: dir === 'rtl' ? 'right' : 'left',
      wordWrap: 'break-word',
      opacity: isVisible ? 1 : 0,
      visibility: isVisible ? 'visible' : 'hidden',
      transform: isVisible ? 'scale(1)' : 'scale(0.95)',
      transition: 'all 150ms ease',
      pointerEvents: 'none',
      boxShadow: 'var(--sid-shadow-lg)',
    };

    // Variant-based colors
    switch (variant) {
      case 'cultural':
        return {
          ...baseStyles,
          backgroundColor: 'var(--sid-wheat-700)',
          color: 'var(--sid-charcoal-0)',
          border: '1px solid var(--sid-wheat-600)',
        };
      case 'dark':
        return {
          ...baseStyles,
          backgroundColor: 'var(--sid-charcoal-900)',
          color: 'var(--sid-charcoal-0)',
          border: '1px solid var(--sid-charcoal-800)',
        };
      case 'light':
        return {
          ...baseStyles,
          backgroundColor: 'var(--sid-charcoal-0)',
          color: 'var(--sid-charcoal-900)',
          border: '1px solid var(--sid-border-primary)',
        };
      default:
        return {
          ...baseStyles,
          backgroundColor: 'var(--sid-charcoal-800)',
          color: 'var(--sid-charcoal-0)',
          border: '1px solid var(--sid-charcoal-700)',
        };
    }
  };

  // Get positioning styles based on placement
  const getPositionStyles = (): React.CSSProperties => {
    const offset = 8; // Distance from trigger element
    
    switch (actualPlacement) {
      case 'top':
        return {
          bottom: '100%',
          left: '50%',
          transform: `translateX(-50%) ${isVisible ? 'translateY(-${offset}px) scale(1)' : 'translateY(-4px) scale(0.95)'}`,
          marginBottom: `${offset}px`,
        };
      case 'bottom':
        return {
          top: '100%',
          left: '50%',
          transform: `translateX(-50%) ${isVisible ? 'translateY(${offset}px) scale(1)' : 'translateY(4px) scale(0.95)'}`,
          marginTop: `${offset}px`,
        };
      case 'left':
        return {
          right: '100%',
          top: '50%',
          transform: `translateY(-50%) ${isVisible ? 'translateX(-${offset}px) scale(1)' : 'translateX(-4px) scale(0.95)'}`,
          marginRight: `${offset}px`,
        };
      case 'right':
        return {
          left: '100%',
          top: '50%',
          transform: `translateY(-50%) ${isVisible ? 'translateX(${offset}px) scale(1)' : 'translateX(4px) scale(0.95)'}`,
          marginLeft: `${offset}px`,
        };
      default:
        return {};
    }
  };

  // Clone child element with event handlers
  const triggerElement = React.cloneElement(children, {
    ref: triggerRef,
    onMouseEnter: (e: React.MouseEvent) => {
      showTooltip();
      children.props.onMouseEnter?.(e);
    },
    onMouseLeave: (e: React.MouseEvent) => {
      hideTooltip();
      children.props.onMouseLeave?.(e);
    },
    onFocus: (e: React.FocusEvent) => {
      showTooltip();
      children.props.onFocus?.(e);
    },
    onBlur: (e: React.FocusEvent) => {
      hideTooltip();
      children.props.onBlur?.(e);
    },
    'aria-describedby': disabled ? undefined : tooltipId,
  });

  return (
    <>
      {triggerElement}
      {!disabled && (
        <div
          ref={tooltipRef}
          id={tooltipId}
          role="tooltip"
          dir={dir}
          style={{
            ...getTooltipStyles(),
            ...getPositionStyles(),
            position: 'absolute',
          }}
        >
          {content}
        </div>
      )}
    </>
  );
};

Tooltip.displayName = 'Tooltip';
