/**
 * Syrian Identity Dialog Component
 * 
 * A beautiful dialog component with Syrian cultural design:
 * - RTL-first design with proper Arabic text support
 * - Cultural color variants inspired by Syrian heritage
 * - Enhanced accessibility with proper ARIA attributes and focus management
 * - Damascus-inspired styling with smooth animations
 * - Backdrop blur and overlay effects
 */

import * as React from 'react';

export type DialogSize = 'sm' | 'md' | 'lg' | 'xl' | 'full';

export interface DialogProps {
  /**
   * Whether the dialog is open.
   */
  open: boolean;
  
  /**
   * Callback when dialog should be closed.
   */
  onClose: () => void;
  
  /**
   * Dialog title (supports Arabic).
   */
  title?: string;
  
  /**
   * Dialog size.
   * @default 'md'
   */
  size?: DialogSize;
  
  /**
   * Whether clicking the backdrop closes the dialog.
   * @default true
   */
  closeOnBackdropClick?: boolean;
  
  /**
   * Whether pressing Escape closes the dialog.
   * @default true
   */
  closeOnEscape?: boolean;
  
  /**
   * Header content (overrides title).
   */
  header?: React.ReactNode;
  
  /**
   * Footer content (actions, buttons, etc.).
   */
  footer?: React.ReactNode;
  
  /**
   * Text direction override. Usually auto-detected from content.
   * @default 'auto'
   */
  dir?: 'rtl' | 'ltr' | 'auto';
  
  /**
   * Dialog content.
   */
  children: React.ReactNode;
}

/**
 * Close Icon Component
 */
const CloseIcon: React.FC = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
  </svg>
);

/**
 * Syrian Identity Dialog Component
 * 
 * @example
 * ```tsx
 * // Arabic dialog with cultural styling
 * <Dialog 
 *   open={isOpen}
 *   onClose={() => setIsOpen(false)}
 *   title="حوار مهم"
 *   dir="rtl"
 *   footer={
 *     <div>
 *       <Button variant="outline" onClick={() => setIsOpen(false)}>إلغاء</Button>
 *       <Button variant="primary">موافق</Button>
 *     </div>
 *   }
 * >
 *   <p>محتوى الحوار باللغة العربية</p>
 * </Dialog>
 * ```
 */
export const Dialog: React.FC<DialogProps> = ({
  open,
  onClose,
  title,
  size = 'md',
  closeOnBackdropClick = true,
  closeOnEscape = true,
  header,
  footer,
  dir = 'auto',
  children,
}) => {
  const dialogRef = React.useRef<HTMLDivElement>(null);
  const previousFocusRef = React.useRef<HTMLElement | null>(null);

  // Handle escape key
  React.useEffect(() => {
    if (!open || !closeOnEscape) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [open, closeOnEscape, onClose]);

  // Focus management
  React.useEffect(() => {
    if (open) {
      // Store current focus
      previousFocusRef.current = document.activeElement as HTMLElement;
      
      // Focus dialog
      setTimeout(() => {
        dialogRef.current?.focus();
      }, 100);
    } else {
      // Restore previous focus
      previousFocusRef.current?.focus();
    }
  }, [open]);

  // Prevent body scroll when dialog is open
  React.useEffect(() => {
    if (open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [open]);

  if (!open) return null;

  // Get dialog styles
  const getBackdropStyles = (): React.CSSProperties => ({
    position: 'fixed',
    inset: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    backdropFilter: 'blur(4px)',
    zIndex: 9999,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 'var(--sid-space-4)',
    opacity: open ? 1 : 0,
    transition: 'opacity 200ms ease',
  });

  const getDialogStyles = (): React.CSSProperties => {
    const sizeMap = {
      sm: { maxWidth: '400px', width: '100%' },
      md: { maxWidth: '500px', width: '100%' },
      lg: { maxWidth: '700px', width: '100%' },
      xl: { maxWidth: '900px', width: '100%' },
      full: { maxWidth: '95vw', width: '95vw', height: '95vh' }
    };

    return {
      backgroundColor: 'var(--sid-bg-primary)',
      borderRadius: 'var(--sid-radius-lg)',
      boxShadow: 'var(--sid-shadow-xl)',
      maxHeight: size === 'full' ? '95vh' : '90vh',
      ...sizeMap[size],
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      transform: open ? 'scale(1)' : 'scale(0.95)',
      transition: 'transform 200ms ease',
      outline: 'none',
      fontFamily: dir === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
    };
  };

  const getHeaderStyles = (): React.CSSProperties => ({
    padding: 'var(--sid-space-6) var(--sid-space-6) var(--sid-space-4)',
    borderBottom: '1px solid var(--sid-border-primary)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    flexShrink: 0,
  });

  const getTitleStyles = (): React.CSSProperties => ({
    fontSize: 'var(--sid-text-xl)',
    fontWeight: 'var(--sid-font-semibold)',
    color: 'var(--sid-text-primary)',
    margin: 0,
    flex: 1,
  });

  const getCloseButtonStyles = (): React.CSSProperties => ({
    background: 'none',
    border: 'none',
    cursor: 'pointer',
    padding: 'var(--sid-space-2)',
    borderRadius: 'var(--sid-radius-md)',
    color: 'var(--sid-text-secondary)',
    transition: 'all 150ms ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginInlineStart: 'var(--sid-space-2)',
  });

  const getContentStyles = (): React.CSSProperties => ({
    padding: 'var(--sid-space-6)',
    flex: 1,
    overflow: 'auto',
    color: 'var(--sid-text-primary)',
    lineHeight: 'var(--sid-leading-relaxed)',
  });

  const getFooterStyles = (): React.CSSProperties => ({
    padding: 'var(--sid-space-4) var(--sid-space-6) var(--sid-space-6)',
    borderTop: '1px solid var(--sid-border-primary)',
    display: 'flex',
    gap: 'var(--sid-space-2)',
    justifyContent: 'flex-end',
    flexDirection: dir === 'rtl' ? 'row-reverse' : 'row',
    flexShrink: 0,
  });

  const handleBackdropClick = (event: React.MouseEvent) => {
    if (closeOnBackdropClick && event.target === event.currentTarget) {
      onClose();
    }
  };

  return (
    <div style={getBackdropStyles()} onClick={handleBackdropClick}>
      <div
        ref={dialogRef}
        role="dialog"
        aria-modal="true"
        aria-labelledby={title ? 'dialog-title' : undefined}
        dir={dir}
        style={getDialogStyles()}
        tabIndex={-1}
      >
        {/* Header */}
        {(header || title) && (
          <div style={getHeaderStyles()}>
            {header || (
              <>
                <h2 id="dialog-title" style={getTitleStyles()}>
                  {title}
                </h2>
                <button
                  type="button"
                  onClick={onClose}
                  style={getCloseButtonStyles()}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--sid-bg-secondary)';
                    e.currentTarget.style.color = 'var(--sid-text-primary)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = 'var(--sid-text-secondary)';
                  }}
                  aria-label={dir === 'rtl' ? 'إغلاق الحوار' : 'Close dialog'}
                >
                  <CloseIcon />
                </button>
              </>
            )}
          </div>
        )}

        {/* Content */}
        <div style={getContentStyles()}>
          {children}
        </div>

        {/* Footer */}
        {footer && (
          <div style={getFooterStyles()}>
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

Dialog.displayName = 'Dialog';
