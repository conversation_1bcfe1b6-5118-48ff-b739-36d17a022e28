/**
 * Syrian Identity UI Components
 * 
 * A comprehensive React component library celebrating Syrian culture
 * with RTL-first design, Arabic typography, and cultural authenticity.
 * 
 * @package @sid/components
 * @version 0.1.0
 */

// Button Component
export { Button } from './button';
export type { ButtonProps, ButtonVariant, ButtonSize } from './button';

// Input Components
export { Input, Textarea, Select } from './input';
export type {
  InputProps, InputVariant, InputSize, InputState,
  TextareaProps, TextareaVariant, TextareaSize, TextareaState,
  SelectProps, SelectVariant, SelectSize, SelectState, SelectOption
} from './input';

// Form Components
export { Checkbox, Radio, RadioGroup, Switch } from './form';
export type {
  CheckboxProps, CheckboxVariant, CheckboxSize,
  RadioProps, RadioGroupProps, RadioVariant, RadioSize,
  SwitchProps, SwitchVariant, SwitchSize
} from './form';

// Layout Components
export { Card, Badge, Alert } from './layout';
export type {
  CardProps, CardVariant, CardPadding,
  BadgeProps, BadgeVariant, BadgeSize, BadgeShape,
  AlertProps, AlertVariant, AlertSize
} from './layout';

// Interactive Components
export { Tooltip, Dialog } from './interactive';
export type {
  TooltipProps, TooltipVariant, TooltipPlacement, TooltipSize,
  DialogProps, DialogSize
} from './interactive';

// Cultural Components
export {
  DamascusRoseIcon, OliveBranchIcon, WheatGrainIcon, DamascusArchIcon,
  SyrianStarIcon, CalligraphyPenIcon, SyrianIcons, GeometricPattern
} from './cultural';
export type {
  IconProps, SyrianIconName, GeometricPatternProps, PatternType, PatternSize
} from './cultural';

// Animation Components
export {
  AnimationProvider, useAnimation, useAnimatedStyles, useStaggerDelay,
  Typewriter, GradientText, MorphingText,
  SyrianSplitText, CulturalTextReveal, TextAnimationTest,
  AnimatedGeometricPattern, ParticleSystem, AnimatedGradient,
  CardReveal, StaggeredList, PageTransition,
  AnimatedInput, MultiStepForm,
  AnimatedDamascusRose, AnimatedOliveBranch, AnimatedWheatGrain,
  GeometricSpinner, ProgressBar, SkeletonLoader,
  ScrollTriggered, Draggable, HoverEffect,
  usePerformanceMonitor, useAccessibilityPreferences, OptimizedAnimation, culturalA11yUtils,
  sidDurations, sidEasing, sidDelays, sidSprings, sidKeyframes,
  sidAnimations, sidAnimationTokens, animationUtils
} from './animations';
export type {
  AnimationProviderProps, TypewriterProps, GradientTextProps, MorphingTextProps,
  SyrianSplitTextProps, CulturalTextRevealProps,
  AnimatedGeometricPatternProps, ParticleSystemProps, AnimatedGradientProps,
  CardRevealProps, StaggeredListProps, PageTransitionProps,
  AnimatedInputProps, MultiStepFormProps,
  AnimatedDamascusRoseProps, AnimatedOliveBranchProps, AnimatedWheatGrainProps,
  GeometricSpinnerProps, ProgressBarProps, SkeletonLoaderProps,
  ScrollTriggeredProps, DraggableProps, HoverEffectProps,
  PerformanceMetrics, AccessibilityPreferences, OptimizedAnimationProps
} from './animations';

// Version and metadata
export const version = '0.1.0';
export const name = '@sid/components';

// Component registry for development tools
export const components = {
  Button: 'button',
  Input: 'input',
  Textarea: 'input',
  Select: 'input',
  Checkbox: 'form',
  Radio: 'form',
  RadioGroup: 'form',
  Switch: 'form',
  Card: 'layout',
  Badge: 'layout',
  Alert: 'layout',
  Tooltip: 'interactive',
  Dialog: 'interactive',
  GeometricPattern: 'cultural',
  SyrianIcons: 'cultural',
  AnimationProvider: 'animations',
  Typewriter: 'animations',
  GradientText: 'animations',
  MorphingText: 'animations',
  AnimatedGeometricPattern: 'animations',
  ParticleSystem: 'animations',
  AnimatedGradient: 'animations',
  CardReveal: 'animations',
  StaggeredList: 'animations',
  PageTransition: 'animations',
  AnimatedInput: 'animations',
  MultiStepForm: 'animations',
  AnimatedDamascusRose: 'animations',
  AnimatedOliveBranch: 'animations',
  AnimatedWheatGrain: 'animations',
  GeometricSpinner: 'animations',
  ProgressBar: 'animations',
  SkeletonLoader: 'animations',
  ScrollTriggered: 'animations',
  Draggable: 'animations',
  HoverEffect: 'animations'
} as const;
