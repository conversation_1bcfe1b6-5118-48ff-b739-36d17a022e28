/**
 * Syrian Identity Textarea Component
 * 
 * A beautiful textarea component for multi-line text input:
 * - Optimized for Arabic text with proper line height
 * - RTL-first design with cultural color variants
 * - Auto-resize functionality for better UX
 * - Enhanced accessibility for screen readers
 * - Damascus-inspired focus states
 */

import * as React from 'react';

export type TextareaVariant = 'default' | 'filled' | 'outlined' | 'cultural';
export type TextareaSize = 'sm' | 'md' | 'lg';
export type TextareaState = 'default' | 'error' | 'success' | 'warning';

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  /**
   * Visual style variant inspired by Syrian design.
   * @default 'default'
   */
  variant?: TextareaVariant;
  
  /**
   * Size scale for different use cases.
   * @default 'md'
   */
  size?: TextareaSize;
  
  /**
   * Visual state for validation feedback.
   * @default 'default'
   */
  state?: TextareaState;
  
  /**
   * Label text (supports Arabic).
   */
  label?: string;
  
  /**
   * Helper text below the textarea.
   */
  helperText?: string;
  
  /**
   * Error message (overrides helperText when state is 'error').
   */
  errorMessage?: string;
  
  /**
   * Whether the textarea should auto-resize based on content.
   * @default false
   */
  autoResize?: boolean;
  
  /**
   * Minimum number of rows when auto-resize is enabled.
   * @default 3
   */
  minRows?: number;
  
  /**
   * Maximum number of rows when auto-resize is enabled.
   * @default 10
   */
  maxRows?: number;
  
  /**
   * Whether the textarea should take full width of its container.
   * @default false
   */
  fullWidth?: boolean;
  
  /**
   * Text direction override. Usually auto-detected from content.
   * @default 'auto'
   */
  dir?: 'rtl' | 'ltr' | 'auto';
}

/**
 * Syrian Identity Textarea Component
 * 
 * @example
 * ```tsx
 * // Arabic textarea with cultural styling
 * <Textarea 
 *   variant="cultural"
 *   label="رسالتك"
 *   placeholder="اكتب رسالتك هنا..."
 *   autoResize
 *   dir="rtl"
 * />
 * 
 * // English textarea with validation
 * <Textarea
 *   label="Message"
 *   state="error"
 *   errorMessage="Message is required"
 *   minRows={4}
 *   dir="ltr"
 * />
 * ```
 */
export const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      variant = 'default',
      size = 'md',
      state = 'default',
      label,
      helperText,
      errorMessage,
      autoResize = false,
      minRows = 3,
      maxRows = 10,
      fullWidth = false,
      dir = 'auto',
      className,
      disabled = false,
      rows = 4,
      ...rest
    },
    ref
  ) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null);
    const textareaId = React.useId();
    const helperTextId = React.useId();
    
    // Combine refs
    React.useImperativeHandle(ref, () => textareaRef.current!);
    
    // Determine helper text based on state
    const displayHelperText = state === 'error' && errorMessage ? errorMessage : helperText;
    
    // Auto-resize functionality
    const adjustHeight = React.useCallback(() => {
      const textarea = textareaRef.current;
      if (!textarea || !autoResize) return;
      
      // Reset height to calculate scrollHeight
      textarea.style.height = 'auto';
      
      // Calculate line height
      const lineHeight = parseInt(getComputedStyle(textarea).lineHeight);
      const minHeight = lineHeight * minRows;
      const maxHeight = lineHeight * maxRows;
      
      // Set new height within bounds
      const newHeight = Math.min(Math.max(textarea.scrollHeight, minHeight), maxHeight);
      textarea.style.height = `${newHeight}px`;
    }, [autoResize, minRows, maxRows]);
    
    // Adjust height on content change
    React.useEffect(() => {
      adjustHeight();
    }, [adjustHeight, rest.value]);
    
    // Get textarea styles
    const getTextareaStyles = (): React.CSSProperties => {
      const baseStyles: React.CSSProperties = {
        fontFamily: 'var(--sid-font-universal)',
        fontSize: size === 'sm' ? 'var(--sid-text-sm)' : size === 'lg' ? 'var(--sid-text-lg)' : 'var(--sid-text-base)',
        lineHeight: 'var(--sid-leading-body)', // Better for Arabic text
        paddingBlock: size === 'sm' ? 'var(--sid-space-2)' : size === 'lg' ? 'var(--sid-space-4)' : 'var(--sid-space-3)',
        paddingInline: size === 'sm' ? 'var(--sid-space-3)' : size === 'lg' ? 'var(--sid-space-5)' : 'var(--sid-space-4)',
        borderRadius: 'var(--sid-radius-input)',
        border: '1px solid',
        outline: 'none',
        transition: 'border-color 150ms ease, box-shadow 150ms ease, background-color 150ms ease',
        inlineSize: fullWidth ? '100%' : 'auto',
        minInlineSize: '300px',
        resize: autoResize ? 'none' : 'vertical',
        opacity: disabled ? 0.6 : 1,
        cursor: disabled ? 'not-allowed' : 'text',
      };

      // State-based colors
      let borderColor = 'var(--sid-border-primary)';
      let backgroundColor = 'var(--sid-bg-primary)';
      
      switch (state) {
        case 'error':
          borderColor = 'var(--sid-flag-red)';
          break;
        case 'success':
          borderColor = 'var(--sid-flag-green)';
          break;
        case 'warning':
          borderColor = 'var(--sid-wheat-600)';
          break;
      }

      // Variant-based styling
      switch (variant) {
        case 'filled':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-bg-secondary)',
            borderColor: 'transparent',
            color: 'var(--sid-text-primary)',
          };
        case 'outlined':
          return {
            ...baseStyles,
            backgroundColor: 'transparent',
            borderColor,
            borderWidth: '2px',
            color: 'var(--sid-text-primary)',
          };
        case 'cultural':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-wheat-50)',
            borderColor: 'var(--sid-wheat-400)',
            color: 'var(--sid-charcoal-900)',
            fontFamily: 'var(--sid-font-arabic)',
            lineHeight: 'var(--sid-leading-relaxed)', // Even better for Arabic paragraphs
          };
        default:
          return {
            ...baseStyles,
            backgroundColor,
            borderColor,
            color: 'var(--sid-text-primary)',
          };
      }
    };

    const getLabelStyles = (): React.CSSProperties => ({
      fontFamily: dir === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
      fontSize: 'var(--sid-text-sm)',
      fontWeight: 'var(--sid-font-medium)',
      color: state === 'error' ? 'var(--sid-flag-red)' : 'var(--sid-text-secondary)',
      marginBlockEnd: 'var(--sid-space-1)',
      display: 'block',
    });

    const getHelperTextStyles = (): React.CSSProperties => ({
      fontFamily: dir === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
      fontSize: 'var(--sid-text-xs)',
      color: state === 'error' ? 'var(--sid-flag-red)' : 
             state === 'success' ? 'var(--sid-flag-green)' :
             state === 'warning' ? 'var(--sid-wheat-700)' : 'var(--sid-text-tertiary)',
      marginBlockStart: 'var(--sid-space-1)',
    });

    const getContainerStyles = (): React.CSSProperties => ({
      display: 'inline-block',
      inlineSize: fullWidth ? '100%' : 'auto',
    });

    return (
      <div style={getContainerStyles()}>
        {label && (
          <label htmlFor={textareaId} style={getLabelStyles()}>
            {label}
          </label>
        )}
        
        <textarea
          ref={textareaRef}
          id={textareaId}
          dir={dir}
          disabled={disabled}
          rows={autoResize ? minRows : rows}
          aria-describedby={displayHelperText ? helperTextId : undefined}
          aria-invalid={state === 'error' || undefined}
          style={getTextareaStyles()}
          className={className}
          onInput={(e) => {
            adjustHeight();
            rest.onInput?.(e);
          }}
          onFocus={(e) => {
            const target = e.target as HTMLTextAreaElement;
            target.style.borderColor = variant === 'cultural' ? 'var(--sid-wheat-600)' : 
                                      state === 'error' ? 'var(--sid-flag-red)' :
                                      state === 'success' ? 'var(--sid-flag-green)' :
                                      state === 'warning' ? 'var(--sid-wheat-600)' :
                                      'var(--sid-forest-500)';
            target.style.boxShadow = 'var(--sid-shadow-focus)';
            rest.onFocus?.(e);
          }}
          onBlur={(e) => {
            const target = e.target as HTMLTextAreaElement;
            target.style.borderColor = state === 'error' ? 'var(--sid-flag-red)' :
                                      state === 'success' ? 'var(--sid-flag-green)' :
                                      state === 'warning' ? 'var(--sid-wheat-600)' :
                                      variant === 'cultural' ? 'var(--sid-wheat-400)' :
                                      'var(--sid-border-primary)';
            target.style.boxShadow = 'none';
            rest.onBlur?.(e);
          }}
          {...rest}
        />
        
        {displayHelperText && (
          <div id={helperTextId} style={getHelperTextStyles()}>
            {displayHelperText}
          </div>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';
