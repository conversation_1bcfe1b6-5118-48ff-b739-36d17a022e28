/**
 * RTL (Right-to-Left) Tests for Input Component
 * 
 * Comprehensive test suite for RTL text alignment, auto-detection,
 * and Arabic/RTL language support in Input components.
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { Input } from '../Input';

// Mock CSS injection for testing
const mockStyleElement = {
  innerHTML: '',
  setAttribute: jest.fn(),
};

Object.defineProperty(document, 'createElement', {
  value: jest.fn(() => mockStyleElement),
});

Object.defineProperty(document.head, 'appendChild', {
  value: jest.fn(),
});

describe('Input RTL Implementation', () => {
  beforeEach(() => {
    mockStyleElement.innerHTML = '';
    jest.clearAllMocks();
  });

  describe('Text Direction Detection', () => {
    test('detects Arabic text as RTL', () => {
      render(
        <Input
          placeholder="أدخل اسمك الكامل"
          dir="auto"
          data-testid="arabic-input"
        />
      );
      
      const input = screen.getByTestId('arabic-input');
      expect(input).toHaveAttribute('dir', 'rtl');
    });

    test('detects English text as LTR', () => {
      render(
        <Input
          placeholder="Enter your full name"
          dir="auto"
          data-testid="english-input"
        />
      );
      
      const input = screen.getByTestId('english-input');
      expect(input).toHaveAttribute('dir', 'ltr');
    });

    test('detects mixed content with Arabic dominance as RTL', () => {
      render(
        <Input
          placeholder="البحث Search المنتجات"
          dir="auto"
          data-testid="mixed-arabic-input"
        />
      );
      
      const input = screen.getByTestId('mixed-arabic-input');
      expect(input).toHaveAttribute('dir', 'rtl');
    });

    test('detects mixed content with English dominance as LTR', () => {
      render(
        <Input
          placeholder="Search البحث products"
          dir="auto"
          data-testid="mixed-english-input"
        />
      );
      
      const input = screen.getByTestId('mixed-english-input');
      expect(input).toHaveAttribute('dir', 'ltr');
    });

    test('handles empty placeholder gracefully', () => {
      render(
        <Input
          placeholder=""
          dir="auto"
          data-testid="empty-input"
        />
      );
      
      const input = screen.getByTestId('empty-input');
      expect(input).toHaveAttribute('dir', 'ltr');
    });

    test('uses value for direction detection when placeholder is empty', () => {
      render(
        <Input
          value="أدخل النص"
          placeholder=""
          dir="auto"
          data-testid="value-based-input"
        />
      );
      
      const input = screen.getByTestId('value-based-input');
      expect(input).toHaveAttribute('dir', 'rtl');
    });
  });

  describe('Explicit Direction Setting', () => {
    test('respects explicit RTL direction', () => {
      render(
        <Input
          placeholder="English text"
          dir="rtl"
          data-testid="explicit-rtl-input"
        />
      );
      
      const input = screen.getByTestId('explicit-rtl-input');
      expect(input).toHaveAttribute('dir', 'rtl');
    });

    test('respects explicit LTR direction', () => {
      render(
        <Input
          placeholder="أدخل النص العربي"
          dir="ltr"
          data-testid="explicit-ltr-input"
        />
      );
      
      const input = screen.getByTestId('explicit-ltr-input');
      expect(input).toHaveAttribute('dir', 'ltr');
    });
  });

  describe('Placeholder Alignment', () => {
    test('generates natural alignment CSS for RTL', () => {
      render(
        <Input
          placeholder="أدخل النص"
          dir="rtl"
          placeholderAlign="natural"
          data-testid="natural-rtl-input"
        />
      );
      
      // Check that CSS was generated
      expect(document.createElement).toHaveBeenCalledWith('style');
      expect(mockStyleElement.innerHTML).toContain('text-align: right');
      expect(mockStyleElement.innerHTML).toContain('direction: rtl');
    });

    test('generates opposite alignment CSS for RTL', () => {
      render(
        <Input
          placeholder="أدخل النص"
          dir="rtl"
          placeholderAlign="opposite"
          data-testid="opposite-rtl-input"
        />
      );
      
      expect(mockStyleElement.innerHTML).toContain('text-align: left');
      expect(mockStyleElement.innerHTML).toContain('direction: ltr');
    });

    test('does not generate placeholder CSS for LTR content', () => {
      render(
        <Input
          placeholder="Enter text"
          dir="ltr"
          placeholderAlign="natural"
          data-testid="ltr-input"
        />
      );
      
      // Should not create style element for LTR
      expect(mockStyleElement.innerHTML).toBe('');
    });

    test('includes unicode-bidi in generated CSS', () => {
      render(
        <Input
          placeholder="أدخل النص"
          dir="rtl"
          placeholderAlign="natural"
          data-testid="unicode-bidi-input"
        />
      );
      
      expect(mockStyleElement.innerHTML).toContain('unicode-bidi: embed');
    });

    test('includes all browser-specific placeholder selectors', () => {
      render(
        <Input
          placeholder="أدخل النص"
          dir="rtl"
          placeholderAlign="natural"
          data-testid="cross-browser-input"
        />
      );
      
      const css = mockStyleElement.innerHTML;
      expect(css).toContain('::placeholder');
      expect(css).toContain('::-webkit-input-placeholder');
      expect(css).toContain('::-moz-placeholder');
      expect(css).toContain(':-ms-input-placeholder');
      expect(css).toContain('::-ms-input-placeholder');
    });
  });

  describe('Label and Helper Text Alignment', () => {
    test('aligns label to right for RTL content', () => {
      render(
        <Input
          label="الاسم الكامل"
          placeholder="أدخل اسمك"
          dir="rtl"
          data-testid="rtl-label-input"
        />
      );
      
      const label = screen.getByText('الاسم الكامل');
      expect(label).toHaveStyle({ textAlign: 'right' });
    });

    test('aligns helper text to right for RTL content', () => {
      render(
        <Input
          helperText="نص مساعد"
          placeholder="أدخل النص"
          dir="rtl"
          data-testid="rtl-helper-input"
        />
      );
      
      const helperText = screen.getByText('نص مساعد');
      expect(helperText).toHaveStyle({ textAlign: 'right' });
    });

    test('uses Arabic font for RTL content', () => {
      render(
        <Input
          label="الاسم"
          helperText="نص مساعد"
          placeholder="أدخل النص"
          dir="rtl"
          data-testid="arabic-font-input"
        />
      );
      
      const label = screen.getByText('الاسم');
      const helperText = screen.getByText('نص مساعد');
      
      expect(label).toHaveStyle({ fontFamily: 'var(--sid-font-arabic)' });
      expect(helperText).toHaveStyle({ fontFamily: 'var(--sid-font-arabic)' });
    });
  });

  describe('Input Text Alignment', () => {
    test('aligns input text to right for RTL', () => {
      render(
        <Input
          placeholder="أدخل النص"
          dir="rtl"
          data-testid="rtl-text-input"
        />
      );
      
      const input = screen.getByTestId('rtl-text-input');
      expect(input).toHaveStyle({ textAlign: 'right' });
    });

    test('aligns input text to left for LTR', () => {
      render(
        <Input
          placeholder="Enter text"
          dir="ltr"
          data-testid="ltr-text-input"
        />
      );
      
      const input = screen.getByTestId('ltr-text-input');
      expect(input).toHaveStyle({ textAlign: 'left' });
    });
  });

  describe('Edge Cases', () => {
    test('handles Hebrew text correctly', () => {
      render(
        <Input
          placeholder="הכנס טקסט"
          dir="auto"
          data-testid="hebrew-input"
        />
      );
      
      const input = screen.getByTestId('hebrew-input');
      expect(input).toHaveAttribute('dir', 'rtl');
    });

    test('handles numbers and symbols correctly', () => {
      render(
        <Input
          placeholder="123 أرقام ورموز !@#"
          dir="auto"
          data-testid="mixed-symbols-input"
        />
      );
      
      const input = screen.getByTestId('mixed-symbols-input');
      expect(input).toHaveAttribute('dir', 'rtl');
    });

    test('handles only numbers as LTR', () => {
      render(
        <Input
          placeholder="123456"
          dir="auto"
          data-testid="numbers-only-input"
        />
      );
      
      const input = screen.getByTestId('numbers-only-input');
      expect(input).toHaveAttribute('dir', 'ltr');
    });

    test('updates direction when placeholder changes', () => {
      const { rerender } = render(
        <Input
          placeholder="Enter text"
          dir="auto"
          data-testid="dynamic-input"
        />
      );
      
      let input = screen.getByTestId('dynamic-input');
      expect(input).toHaveAttribute('dir', 'ltr');
      
      rerender(
        <Input
          placeholder="أدخل النص"
          dir="auto"
          data-testid="dynamic-input"
        />
      );
      
      input = screen.getByTestId('dynamic-input');
      expect(input).toHaveAttribute('dir', 'rtl');
    });
  });

  describe('Validation States with RTL', () => {
    test('displays RTL error message correctly', () => {
      render(
        <Input
          placeholder="أدخل البريد"
          state="error"
          errorMessage="البريد الإلكتروني غير صحيح"
          dir="rtl"
          data-testid="rtl-error-input"
        />
      );
      
      const errorMessage = screen.getByText('البريد الإلكتروني غير صحيح');
      expect(errorMessage).toHaveStyle({ 
        textAlign: 'right',
        fontFamily: 'var(--sid-font-arabic)'
      });
    });

    test('displays RTL success message correctly', () => {
      render(
        <Input
          placeholder="أدخل كلمة المرور"
          state="success"
          helperText="كلمة المرور قوية"
          dir="rtl"
          data-testid="rtl-success-input"
        />
      );
      
      const successMessage = screen.getByText('كلمة المرور قوية');
      expect(successMessage).toHaveStyle({ 
        textAlign: 'right',
        fontFamily: 'var(--sid-font-arabic)'
      });
    });
  });
});
