/**
 * Syrian Identity Input Component
 * 
 * A beautiful input component designed for Syrian cultural context:
 * - RTL-first design with Arabic text optimization
 * - Syrian cultural color variants
 * - Enhanced accessibility and screen reader support
 * - Beautiful focus states with Damascus-inspired styling
 * - Support for Arabic placeholder text and labels
 */

import * as React from 'react';
import { clsx } from 'clsx';

/**
 * Detects text direction based on content using Unicode bidirectional algorithm
 */
const detectTextDirection = (text: string): 'rtl' | 'ltr' => {
  if (!text) return 'ltr';

  // Arabic, Hebrew, and other RTL Unicode ranges
  const rtlChars = /[\u0590-\u05FF\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const ltrChars = /[A-Za-z]/;

  const rtlCount = (text.match(rtlChars) || []).length;
  const ltrCount = (text.match(ltrChars) || []).length;

  // If we have RTL characters and they outnumber LTR, it's RTL
  if (rtlCount > 0 && rtlCount >= ltrCount) {
    return 'rtl';
  }

  return 'ltr';
};

export type InputVariant = 'default' | 'filled' | 'outlined' | 'cultural';
export type InputSize = 'sm' | 'md' | 'lg';
export type InputState = 'default' | 'error' | 'success' | 'warning';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /**
   * Visual style variant inspired by Syrian design.
   * @default 'default'
   */
  variant?: InputVariant;
  
  /**
   * Size scale for different use cases.
   * @default 'md'
   */
  size?: InputSize;
  
  /**
   * Visual state for validation feedback.
   * @default 'default'
   */
  state?: InputState;
  
  /**
   * Label text (supports Arabic).
   */
  label?: string;
  
  /**
   * Helper text below the input.
   */
  helperText?: string;
  
  /**
   * Error message (overrides helperText when state is 'error').
   */
  errorMessage?: string;
  
  /**
   * Icon to display at the start of the input (respects RTL).
   */
  startIcon?: React.ReactNode;
  
  /**
   * Icon to display at the end of the input (respects RTL).
   */
  endIcon?: React.ReactNode;
  
  /**
   * Whether the input should take full width of its container.
   * @default false
   */
  fullWidth?: boolean;
  
  /**
   * Text direction override.
   * 'auto' - automatically detects direction from placeholder/value content
   * 'rtl' - forces right-to-left direction
   * 'ltr' - forces left-to-right direction
   * @default 'auto'
   */
  dir?: 'rtl' | 'ltr' | 'auto';

  /**
   * Placeholder alignment behavior in RTL contexts.
   * 'natural' - placeholder follows text direction (Arabic UX standard)
   * 'opposite' - placeholder starts opposite to text (shows typing destination)
   * @default 'natural'
   */
  placeholderAlign?: 'natural' | 'opposite';
}

/**
 * Syrian Identity Input Component
 * 
 * @example
 * ```tsx
 * // Arabic input with cultural styling
 * <Input 
 *   variant="cultural"
 *   label="الاسم الكامل"
 *   placeholder="أدخل اسمك الكامل"
 *   dir="rtl"
 * />
 * 
 * // English input with validation
 * <Input
 *   label="Email Address"
 *   type="email"
 *   state="error"
 *   errorMessage="Please enter a valid email"
 *   dir="ltr"
 * />
 * ```
 */
export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      variant = 'default',
      size = 'md',
      state = 'default',
      label,
      helperText,
      errorMessage,
      startIcon,
      endIcon,
      fullWidth = false,
      dir = 'auto',
      placeholderAlign = 'natural',
      className,
      disabled = false,
      ...rest
    },
    ref
  ) => {
    const inputId = React.useId();
    const helperTextId = React.useId();

    // Determine helper text based on state
    const displayHelperText = state === 'error' && errorMessage ? errorMessage : helperText;

    // Determine actual text direction
    const actualDirection = React.useMemo(() => {
      if (dir === 'rtl' || dir === 'ltr') return dir;

      // Auto-detect from placeholder or current value
      const textToAnalyze = rest.value || rest.defaultValue || rest.placeholder || '';
      return detectTextDirection(String(textToAnalyze));
    }, [dir, rest.value, rest.defaultValue, rest.placeholder]);
    
    // Get variant styles
    const getInputStyles = (): React.CSSProperties => {
      const baseStyles: React.CSSProperties = {
        fontFamily: 'var(--sid-font-universal)',
        fontSize: size === 'sm' ? 'var(--sid-text-sm)' : size === 'lg' ? 'var(--sid-text-lg)' : 'var(--sid-text-base)',
        lineHeight: 'var(--sid-leading-normal)',
        paddingBlock: size === 'sm' ? 'var(--sid-space-2)' : size === 'lg' ? 'var(--sid-space-4)' : 'var(--sid-space-3)',
        paddingInline: size === 'sm' ? 'var(--sid-space-3)' : size === 'lg' ? 'var(--sid-space-5)' : 'var(--sid-space-4)',
        borderRadius: 'var(--sid-radius-input)',
        border: '1px solid',
        outline: 'none',
        transition: 'border-color 150ms ease, box-shadow 150ms ease, background-color 150ms ease',
        inlineSize: fullWidth ? '100%' : 'auto',
        minInlineSize: '200px',
        opacity: disabled ? 0.6 : 1,
        cursor: disabled ? 'not-allowed' : 'text',
        // RTL-aware text alignment for better UX
        textAlign: actualDirection === 'rtl' ? 'right' : 'left',
      };

      // State-based colors
      let borderColor = 'var(--sid-border-primary)';
      let focusBorderColor = 'var(--sid-forest-500)';
      let backgroundColor = 'var(--sid-bg-primary)';
      
      switch (state) {
        case 'error':
          borderColor = 'var(--sid-flag-red)';
          focusBorderColor = 'var(--sid-flag-red)';
          break;
        case 'success':
          borderColor = 'var(--sid-flag-green)';
          focusBorderColor = 'var(--sid-flag-green)';
          break;
        case 'warning':
          borderColor = 'var(--sid-wheat-600)';
          focusBorderColor = 'var(--sid-wheat-600)';
          break;
      }

      // Variant-based styling
      switch (variant) {
        case 'filled':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-bg-secondary)',
            borderColor: 'transparent',
            color: 'var(--sid-text-primary)',
          };
        case 'outlined':
          return {
            ...baseStyles,
            backgroundColor: 'transparent',
            borderColor,
            borderWidth: '2px',
            color: 'var(--sid-text-primary)',
          };
        case 'cultural':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-wheat-50)',
            borderColor: 'var(--sid-wheat-400)',
            color: 'var(--sid-charcoal-900)',
            fontFamily: 'var(--sid-font-arabic)',
          };
        default:
          return {
            ...baseStyles,
            backgroundColor,
            borderColor,
            color: 'var(--sid-text-primary)',
          };
      }
    };

    const getLabelStyles = (): React.CSSProperties => ({
      fontFamily: actualDirection === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
      fontSize: 'var(--sid-text-sm)',
      fontWeight: 'var(--sid-font-medium)',
      color: state === 'error' ? 'var(--sid-flag-red)' : 'var(--sid-text-secondary)',
      marginBlockEnd: 'var(--sid-space-1)',
      display: 'block',
      textAlign: actualDirection === 'rtl' ? 'right' : 'left',
    });

    const getHelperTextStyles = (): React.CSSProperties => ({
      fontFamily: actualDirection === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
      fontSize: 'var(--sid-text-xs)',
      color: state === 'error' ? 'var(--sid-flag-red)' :
             state === 'success' ? 'var(--sid-flag-green)' :
             state === 'warning' ? 'var(--sid-wheat-700)' : 'var(--sid-text-tertiary)',
      marginBlockStart: 'var(--sid-space-1)',
      textAlign: actualDirection === 'rtl' ? 'right' : 'left',
    });

    const getContainerStyles = (): React.CSSProperties => ({
      position: 'relative',
      display: 'inline-block',
      inlineSize: fullWidth ? '100%' : 'auto',
    });

    const getIconStyles = (): React.CSSProperties => ({
      position: 'absolute',
      insetBlockStart: '50%',
      transform: 'translateY(-50%)',
      inlineSize: '1rem',
      blockSize: '1rem',
      color: 'var(--sid-text-tertiary)',
      pointerEvents: 'none',
      zIndex: 1,
    });

    // Generate unique class name for placeholder styling
    const placeholderClass = `sid-input-${inputId}`;

    // Create placeholder CSS based on alignment preference and actual direction
    const placeholderCSS = React.useMemo(() => {
      // Only apply custom placeholder styles for RTL content
      if (actualDirection !== 'rtl') return '';

      const alignment = placeholderAlign === 'opposite' ? 'left' : 'right';
      const direction = placeholderAlign === 'natural' ? 'rtl' : 'ltr';

      return `
        .${placeholderClass}::placeholder {
          text-align: ${alignment};
          direction: ${direction};
          unicode-bidi: embed;
        }
        .${placeholderClass}::-webkit-input-placeholder {
          text-align: ${alignment};
          direction: ${direction};
          unicode-bidi: embed;
        }
        .${placeholderClass}::-moz-placeholder {
          text-align: ${alignment};
          direction: ${direction};
          unicode-bidi: embed;
          opacity: 1;
        }
        .${placeholderClass}:-ms-input-placeholder {
          text-align: ${alignment};
          direction: ${direction};
        }
        .${placeholderClass}::-ms-input-placeholder {
          text-align: ${alignment};
          direction: ${direction};
        }
      `;
    }, [placeholderClass, actualDirection, placeholderAlign]);

    return (
      <div style={getContainerStyles()}>
        {/* Inject placeholder styles */}
        {placeholderCSS && (
          <style dangerouslySetInnerHTML={{ __html: placeholderCSS }} />
        )}

        {label && (
          <label htmlFor={inputId} style={getLabelStyles()}>
            {label}
          </label>
        )}
        
        <div style={{ position: 'relative' }}>
          {startIcon && (
            <span 
              style={{
                ...getIconStyles(),
                insetInlineStart: size === 'sm' ? 'var(--sid-space-3)' : 'var(--sid-space-4)',
              }}
              aria-hidden="true"
            >
              {startIcon}
            </span>
          )}
          
          <input
            ref={ref}
            id={inputId}
            dir={actualDirection}
            disabled={disabled}
            aria-describedby={displayHelperText ? helperTextId : undefined}
            aria-invalid={state === 'error' || undefined}
            style={{
              ...getInputStyles(),
              paddingInlineStart: startIcon ? 
                (size === 'sm' ? 'var(--sid-space-10)' : 'var(--sid-space-12)') : 
                undefined,
              paddingInlineEnd: endIcon ? 
                (size === 'sm' ? 'var(--sid-space-10)' : 'var(--sid-space-12)') : 
                undefined,
            }}
            className={`${placeholderClass} ${className || ''}`}
            onFocus={(e) => {
              e.target.style.borderColor = variant === 'cultural' ? 'var(--sid-wheat-600)' : 
                                          state === 'error' ? 'var(--sid-flag-red)' :
                                          state === 'success' ? 'var(--sid-flag-green)' :
                                          state === 'warning' ? 'var(--sid-wheat-600)' :
                                          'var(--sid-forest-500)';
              e.target.style.boxShadow = 'var(--sid-shadow-focus)';
              rest.onFocus?.(e);
            }}
            onBlur={(e) => {
              e.target.style.borderColor = state === 'error' ? 'var(--sid-flag-red)' :
                                          state === 'success' ? 'var(--sid-flag-green)' :
                                          state === 'warning' ? 'var(--sid-wheat-600)' :
                                          variant === 'cultural' ? 'var(--sid-wheat-400)' :
                                          'var(--sid-border-primary)';
              e.target.style.boxShadow = 'none';
              rest.onBlur?.(e);
            }}
            {...rest}
          />
          
          {endIcon && (
            <span 
              style={{
                ...getIconStyles(),
                insetInlineEnd: size === 'sm' ? 'var(--sid-space-3)' : 'var(--sid-space-4)',
              }}
              aria-hidden="true"
            >
              {endIcon}
            </span>
          )}
        </div>
        
        {displayHelperText && (
          <div id={helperTextId} style={getHelperTextStyles()}>
            {displayHelperText}
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';
