/**
 * Syrian Identity Select Component
 * 
 * A beautiful select dropdown component with Syrian cultural design:
 * - RTL-first design with proper Arabic text handling
 * - Cultural color variants inspired by Syrian heritage
 * - Enhanced accessibility with keyboard navigation
 * - Beautiful focus states with Damascus-inspired styling
 * - Support for Arabic option labels
 */

import * as React from 'react';

export type SelectVariant = 'default' | 'filled' | 'outlined' | 'cultural';
export type SelectSize = 'sm' | 'md' | 'lg';
export type SelectState = 'default' | 'error' | 'success' | 'warning';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'size'> {
  /**
   * Visual style variant inspired by Syrian design.
   * @default 'default'
   */
  variant?: SelectVariant;
  
  /**
   * Size scale for different use cases.
   * @default 'md'
   */
  size?: SelectSize;
  
  /**
   * Visual state for validation feedback.
   * @default 'default'
   */
  state?: SelectState;
  
  /**
   * Label text (supports Arabic).
   */
  label?: string;
  
  /**
   * Helper text below the select.
   */
  helperText?: string;
  
  /**
   * Error message (overrides helperText when state is 'error').
   */
  errorMessage?: string;
  
  /**
   * Placeholder text when no option is selected.
   */
  placeholder?: string;
  
  /**
   * Array of options to display.
   */
  options: SelectOption[];
  
  /**
   * Whether the select should take full width of its container.
   * @default false
   */
  fullWidth?: boolean;
  
  /**
   * Text direction override. Usually auto-detected from content.
   * @default 'auto'
   */
  dir?: 'rtl' | 'ltr' | 'auto';
}

/**
 * Chevron Down Icon for Select
 */
const ChevronDownIcon: React.FC = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M4 6l4 4 4-4H4z"/>
  </svg>
);

/**
 * Syrian Identity Select Component
 * 
 * @example
 * ```tsx
 * // Arabic select with cultural styling
 * <Select 
 *   variant="cultural"
 *   label="المحافظة"
 *   placeholder="اختر المحافظة"
 *   options={[
 *     { value: 'damascus', label: 'دمشق' },
 *     { value: 'aleppo', label: 'حلب' },
 *     { value: 'homs', label: 'حمص' }
 *   ]}
 *   dir="rtl"
 * />
 * 
 * // English select with validation
 * <Select
 *   label="Country"
 *   placeholder="Select country"
 *   state="error"
 *   errorMessage="Please select a country"
 *   options={[
 *     { value: 'sy', label: 'Syria' },
 *     { value: 'us', label: 'United States' }
 *   ]}
 *   dir="ltr"
 * />
 * ```
 */
export const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      variant = 'default',
      size = 'md',
      state = 'default',
      label,
      helperText,
      errorMessage,
      placeholder,
      options,
      fullWidth = false,
      dir = 'auto',
      className,
      disabled = false,
      ...rest
    },
    ref
  ) => {
    const selectId = React.useId();
    const helperTextId = React.useId();
    
    // Determine helper text based on state
    const displayHelperText = state === 'error' && errorMessage ? errorMessage : helperText;
    
    // Get select styles
    const getSelectStyles = (): React.CSSProperties => {
      const baseStyles: React.CSSProperties = {
        fontFamily: 'var(--sid-font-universal)',
        fontSize: size === 'sm' ? 'var(--sid-text-sm)' : size === 'lg' ? 'var(--sid-text-lg)' : 'var(--sid-text-base)',
        lineHeight: 'var(--sid-leading-normal)',
        paddingBlock: size === 'sm' ? 'var(--sid-space-2)' : size === 'lg' ? 'var(--sid-space-4)' : 'var(--sid-space-3)',
        paddingInline: size === 'sm' ? 'var(--sid-space-3)' : size === 'lg' ? 'var(--sid-space-5)' : 'var(--sid-space-4)',
        paddingInlineEnd: size === 'sm' ? 'var(--sid-space-8)' : size === 'lg' ? 'var(--sid-space-12)' : 'var(--sid-space-10)',
        borderRadius: 'var(--sid-radius-input)',
        border: '1px solid',
        outline: 'none',
        transition: 'border-color 150ms ease, box-shadow 150ms ease, background-color 150ms ease',
        inlineSize: fullWidth ? '100%' : 'auto',
        minInlineSize: '200px',
        opacity: disabled ? 0.6 : 1,
        cursor: disabled ? 'not-allowed' : 'pointer',
        appearance: 'none',
        backgroundImage: 'none',
      };

      // State-based colors
      let borderColor = 'var(--sid-border-primary)';
      let backgroundColor = 'var(--sid-bg-primary)';
      
      switch (state) {
        case 'error':
          borderColor = 'var(--sid-flag-red)';
          break;
        case 'success':
          borderColor = 'var(--sid-flag-green)';
          break;
        case 'warning':
          borderColor = 'var(--sid-wheat-600)';
          break;
      }

      // Variant-based styling
      switch (variant) {
        case 'filled':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-bg-secondary)',
            borderColor: 'transparent',
            color: 'var(--sid-text-primary)',
          };
        case 'outlined':
          return {
            ...baseStyles,
            backgroundColor: 'transparent',
            borderColor,
            borderWidth: '2px',
            color: 'var(--sid-text-primary)',
          };
        case 'cultural':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-wheat-50)',
            borderColor: 'var(--sid-wheat-400)',
            color: 'var(--sid-charcoal-900)',
            fontFamily: 'var(--sid-font-arabic)',
          };
        default:
          return {
            ...baseStyles,
            backgroundColor,
            borderColor,
            color: 'var(--sid-text-primary)',
          };
      }
    };

    const getLabelStyles = (): React.CSSProperties => ({
      fontFamily: dir === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
      fontSize: 'var(--sid-text-sm)',
      fontWeight: 'var(--sid-font-medium)',
      color: state === 'error' ? 'var(--sid-flag-red)' : 'var(--sid-text-secondary)',
      marginBlockEnd: 'var(--sid-space-1)',
      display: 'block',
    });

    const getHelperTextStyles = (): React.CSSProperties => ({
      fontFamily: dir === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-universal)',
      fontSize: 'var(--sid-text-xs)',
      color: state === 'error' ? 'var(--sid-flag-red)' : 
             state === 'success' ? 'var(--sid-flag-green)' :
             state === 'warning' ? 'var(--sid-wheat-700)' : 'var(--sid-text-tertiary)',
      marginBlockStart: 'var(--sid-space-1)',
    });

    const getContainerStyles = (): React.CSSProperties => ({
      position: 'relative',
      display: 'inline-block',
      inlineSize: fullWidth ? '100%' : 'auto',
    });

    const getIconStyles = (): React.CSSProperties => ({
      position: 'absolute',
      insetBlockStart: '50%',
      insetInlineEnd: size === 'sm' ? 'var(--sid-space-3)' : 'var(--sid-space-4)',
      transform: 'translateY(-50%)',
      color: 'var(--sid-text-tertiary)',
      pointerEvents: 'none',
      zIndex: 1,
    });

    return (
      <div style={getContainerStyles()}>
        {label && (
          <label htmlFor={selectId} style={getLabelStyles()}>
            {label}
          </label>
        )}
        
        <div style={{ position: 'relative' }}>
          <select
            ref={ref}
            id={selectId}
            dir={dir}
            disabled={disabled}
            aria-describedby={displayHelperText ? helperTextId : undefined}
            aria-invalid={state === 'error' || undefined}
            style={getSelectStyles()}
            className={className}
            onFocus={(e) => {
              e.target.style.borderColor = variant === 'cultural' ? 'var(--sid-wheat-600)' : 
                                          state === 'error' ? 'var(--sid-flag-red)' :
                                          state === 'success' ? 'var(--sid-flag-green)' :
                                          state === 'warning' ? 'var(--sid-wheat-600)' :
                                          'var(--sid-forest-500)';
              e.target.style.boxShadow = 'var(--sid-shadow-focus)';
              rest.onFocus?.(e);
            }}
            onBlur={(e) => {
              e.target.style.borderColor = state === 'error' ? 'var(--sid-flag-red)' :
                                          state === 'success' ? 'var(--sid-flag-green)' :
                                          state === 'warning' ? 'var(--sid-wheat-600)' :
                                          variant === 'cultural' ? 'var(--sid-wheat-400)' :
                                          'var(--sid-border-primary)';
              e.target.style.boxShadow = 'none';
              rest.onBlur?.(e);
            }}
            {...rest}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option
                key={option.value}
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ))}
          </select>
          
          <span style={getIconStyles()} aria-hidden="true">
            <ChevronDownIcon />
          </span>
        </div>
        
        {displayHelperText && (
          <div id={helperTextId} style={getHelperTextStyles()}>
            {displayHelperText}
          </div>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';
