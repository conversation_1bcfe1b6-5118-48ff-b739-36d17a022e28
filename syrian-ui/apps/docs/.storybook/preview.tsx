/**
 * Syrian Identity UI - Storybook Preview Configuration
 * 
 * Features:
 * - RTL/LTR direction toggle
 * - Syrian design tokens injection
 * - Arabic typography support
 * - Accessibility testing
 * - Cultural context examples
 */

import type { Preview } from '@storybook/react';
import React, { useEffect } from 'react';

// Inline Syrian design tokens CSS for Storybook
const sidCssVars = `
:root {
  /* === COLORS === */

  /* Flag Colors */
  --sid-flag-red: #CE1126;
  --sid-flag-white: #FFFFFF;
  --sid-flag-black: #000000;
  --sid-flag-green: #007A3D;

  /* Forest Scale */
  --sid-forest-50: #F0F9F7;
  --sid-forest-100: #D1F2E8;
  --sid-forest-200: #A3E5D1;
  --sid-forest-300: #6DD4B6;
  --sid-forest-400: #42BF9A;
  --sid-forest-500: #428177;
  --sid-forest-600: #357A6B;
  --sid-forest-700: #054239;
  --sid-forest-800: #043530;
  --sid-forest-900: #002623;
  --sid-forest-950: #001A18;

  /* Wheat Scale */
  --sid-wheat-50: #FEFCF8;
  --sid-wheat-100: #EDEBE0;
  --sid-wheat-200: #E2DDD0;
  --sid-wheat-300: #D4CDB8;
  --sid-wheat-400: #B9A779;
  --sid-wheat-500: #A69660;
  --sid-wheat-600: #988561;
  --sid-wheat-700: #7A6B4D;
  --sid-wheat-800: #5C5139;
  --sid-wheat-900: #3E3626;
  --sid-wheat-950: #2A2419;

  /* Charcoal Scale */
  --sid-charcoal-0: #FFFFFF;
  --sid-charcoal-50: #F8F8F8;
  --sid-charcoal-100: #F0F0F0;
  --sid-charcoal-200: #E4E4E4;
  --sid-charcoal-300: #D1D1D1;
  --sid-charcoal-400: #B4B4B4;
  --sid-charcoal-500: #9A9A9A;
  --sid-charcoal-600: #3D3A3B;
  --sid-charcoal-700: #2D2A2B;
  --sid-charcoal-800: #1F1C1D;
  --sid-charcoal-900: #161616;
  --sid-charcoal-950: #0A0A0A;

  /* Text Colors */
  --sid-text-primary: #161616;
  --sid-text-secondary: #3D3A3B;
  --sid-text-tertiary: #9A9A9A;
  --sid-text-inverse: #FFFFFF;
  --sid-text-on-color: #FFFFFF;

  /* Background Colors */
  --sid-bg-primary: #FFFFFF;
  --sid-bg-secondary: #F8F8F8;
  --sid-bg-tertiary: #F0F0F0;
  --sid-bg-inverse: #161616;

  /* Border Colors */
  --sid-border-primary: #E4E4E4;
  --sid-border-secondary: #D1D1D1;
  --sid-border-focus: #428177;
  --sid-border-error: #CE1126;

  /* === TYPOGRAPHY === */

  /* Font Families */
  --sid-font-arabic: 'Qomra', 'Noto Kufi Arabic', 'Amiri', 'Tahoma', 'Arial Unicode MS', system-ui, sans-serif;
  --sid-font-latin: 'Inter', 'SF Pro Display', system-ui, -apple-system, 'Segoe UI', 'Roboto', sans-serif;
  --sid-font-universal: 'Inter', 'Noto Kufi Arabic', system-ui, -apple-system, 'Segoe UI', 'Tahoma', sans-serif;

  /* Font Sizes */
  --sid-text-xs: 0.75rem;
  --sid-text-sm: 0.875rem;
  --sid-text-base: 1rem;
  --sid-text-lg: 1.125rem;
  --sid-text-xl: 1.25rem;
  --sid-text-2xl: 1.5rem;

  /* Font Weights */
  --sid-font-light: 300;
  --sid-font-regular: 400;
  --sid-font-medium: 500;
  --sid-font-semibold: 600;
  --sid-font-bold: 700;

  /* Line Heights */
  --sid-leading-normal: 1.5;
  --sid-leading-heading: 1.2;
  --sid-leading-body: 1.6;

  /* === SPACING === */

  /* Space Scale */
  --sid-space-1: 4px;
  --sid-space-2: 8px;
  --sid-space-3: 12px;
  --sid-space-4: 16px;
  --sid-space-44: 44px;

  /* Border Radius */
  --sid-radius-button: 6px;
  --sid-radius-input: 4px;
  --sid-radius-card: 8px;

  /* Shadows */
  --sid-shadow-button: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --sid-shadow-focus: 0 0 0 3px rgba(66, 129, 119, 0.1);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
`;

// Inject Syrian design tokens
const TokensDecorator = (Story: any) => {
  useEffect(() => {
    // Inject tokens CSS if not already present
    if (!document.getElementById('sid-tokens')) {
      const style = document.createElement('style');
      style.id = 'sid-tokens';
      style.textContent = sidCssVars;
      document.head.appendChild(style);
    }
  }, []);

  return <Story />;
};

// RTL/LTR Direction Decorator
const DirectionDecorator = (Story: any, context: any) => {
  const direction = context.globals.direction || 'rtl';
  
  useEffect(() => {
    document.documentElement.setAttribute('dir', direction);
    document.documentElement.setAttribute('lang', direction === 'rtl' ? 'ar' : 'en');
  }, [direction]);

  return (
    <div dir={direction} style={{ minHeight: '100vh', padding: '1rem' }}>
      <Story />
    </div>
  );
};

// Theme decorator for consistent styling
const ThemeDecorator = (Story: any) => {
  return (
    <div style={{ 
      fontFamily: 'var(--sid-font-universal)',
      backgroundColor: 'var(--sid-bg-primary)',
      color: 'var(--sid-text-primary)',
      minHeight: '100vh'
    }}>
      <Story />
    </div>
  );
};

const preview: Preview = {
  parameters: {
    // Default viewport
    viewport: {
      viewports: {
        mobile: {
          name: 'Mobile',
          styles: { width: '375px', height: '667px' }
        },
        tablet: {
          name: 'Tablet', 
          styles: { width: '768px', height: '1024px' }
        },
        desktop: {
          name: 'Desktop',
          styles: { width: '1280px', height: '800px' }
        }
      }
    },
    
    // Actions configuration
    actions: { argTypesRegex: '^on[A-Z].*' },
    
    // Controls configuration
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    
    // Accessibility configuration
    a11y: {
      config: {
        rules: [
          {
            id: 'color-contrast',
            enabled: true,
          },
          {
            id: 'focus-order-semantics', 
            enabled: true,
          },
          {
            id: 'keyboard-navigation',
            enabled: true,
          }
        ]
      }
    },
    
    // Documentation
    docs: {
      toc: true,
    },
    
    // Background options
    backgrounds: {
      default: 'light',
      values: [
        {
          name: 'light',
          value: '#FFFFFF',
        },
        {
          name: 'wheat',
          value: '#EDEBE0',
        },
        {
          name: 'forest',
          value: '#F0F9F7',
        },
        {
          name: 'dark',
          value: '#161616',
        },
      ],
    },
  },

  // Global types for toolbar controls
  globalTypes: {
    direction: {
      name: 'Direction',
      description: 'Text direction for RTL/LTR testing',
      defaultValue: 'rtl',
      toolbar: {
        icon: 'transfer',
        items: [
          { value: 'rtl', title: 'RTL (Arabic)', left: '🇸🇾' },
          { value: 'ltr', title: 'LTR (English)', left: '🇺🇸' },
          { value: 'auto', title: 'Auto', left: '🌐' },
        ],
        showName: true,
      },
    },
    
    locale: {
      name: 'Locale',
      description: 'Language and cultural context',
      defaultValue: 'ar-SY',
      toolbar: {
        icon: 'globe',
        items: [
          { value: 'ar-SY', title: 'Arabic (Syria)', left: '🇸🇾' },
          { value: 'en-US', title: 'English (US)', left: '🇺🇸' },
          { value: 'ar-EG', title: 'Arabic (Egypt)', left: '🇪🇬' },
          { value: 'ar-SA', title: 'Arabic (Saudi)', left: '🇸🇦' },
        ],
        showName: true,
      },
    },
  },

  // Decorators applied to all stories
  decorators: [
    TokensDecorator,
    DirectionDecorator, 
    ThemeDecorator,
  ],

  // Default args for all stories
  args: {
    dir: 'auto',
  },

  // Tags
  tags: ['autodocs'],
};

export default preview;
