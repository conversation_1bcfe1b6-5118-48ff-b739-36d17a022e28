/**
 * Syrian Identity Animation Components Stories
 * 
 * Showcasing beautiful animations inspired by Syrian culture and Islamic art.
 */

import type { <PERSON>a, StoryObj } from '@storybook/react';
import { 
  AnimationProvider, 
  Typewriter, 
  GradientText, 
  MorphingText 
} from '@sid/components';

// Animation Provider Stories
const meta: Meta<typeof AnimationProvider> = {
  title: 'Animations/AnimationProvider',
  component: AnimationProvider,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
The AnimationProvider manages animation preferences and provides utilities across the app.
It automatically respects user motion preferences and provides cultural timing.

**Cultural Context:**
- Animation timing based on Syrian musical rhythms (Maqam)
- Easing curves inspired by Arabic calligraphy flow
- Respects accessibility preferences for reduced motion
        `
      }
    }
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Animation Provider
export const Default: Story = {
  args: {},
  render: (args) => (
    <AnimationProvider {...args}>
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <h3>Animation Provider Active</h3>
        <p>All child components will have access to animation utilities.</p>
      </div>
    </AnimationProvider>
  ),
};

// Typewriter Stories
const TypewriterMeta: Meta<typeof Typewriter> = {
  title: 'Animations/Typewriter',
  component: Typewriter,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
The Typewriter component creates a typewriter effect with cultural timing and RTL support.
Perfect for hero sections, introductions, and storytelling.

**Features:**
- RTL and Arabic text support
- Cultural timing based on Syrian rhythms
- Customizable cursor and speed
- Loop functionality
- Accessibility compliant (respects reduced motion)
        `
      }
    }
  },
  decorators: [
    (Story) => (
      <AnimationProvider>
        <div style={{ padding: '2rem', minHeight: '200px' }}>
          <Story />
        </div>
      </AnimationProvider>
    ),
  ],
  tags: ['autodocs'],
};

// Arabic Typewriter
export const ArabicTypewriter: StoryObj<typeof Typewriter> = {
  args: {
    text: 'مرحباً بكم في سوريا الجميلة، أرض الحضارات والتاريخ العريق',
    speed: 120,
    showCursor: true,
    cursor: '|',
  },
  parameters: {
    docs: {
      description: {
        story: 'Typewriter effect with Arabic text, showcasing RTL support and cultural timing.'
      }
    }
  }
};

// English Typewriter
export const EnglishTypewriter: StoryObj<typeof Typewriter> = {
  args: {
    text: 'Welcome to Beautiful Syria, Land of Ancient Civilizations',
    speed: 80,
    showCursor: true,
    cursor: '_',
  },
  parameters: {
    docs: {
      description: {
        story: 'Fast typewriter effect with English text and custom cursor.'
      }
    }
  }
};

// Looping Typewriter
export const LoopingTypewriter: StoryObj<typeof Typewriter> = {
  args: {
    text: 'أهلاً وسهلاً • Welcome • Bienvenue',
    speed: 100,
    showCursor: true,
    loop: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Looping typewriter with multilingual welcome message.'
      }
    }
  }
};

// Gradient Text Stories
const GradientTextMeta: Meta<typeof GradientText> = {
  title: 'Animations/GradientText',
  component: GradientText,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
The GradientText component creates beautiful gradient text animations using Syrian cultural colors.
Perfect for headings, hero text, and emphasis.

**Features:**
- Syrian flag and cultural color presets
- Multiple animation directions
- Customizable gradient colors
- Smooth, continuous animation
- Accessibility compliant
        `
      }
    }
  },
  decorators: [
    (Story) => (
      <AnimationProvider>
        <div style={{ padding: '2rem', fontSize: '2rem', fontWeight: 'bold' }}>
          <Story />
        </div>
      </AnimationProvider>
    ),
  ],
  tags: ['autodocs'],
};

// Syrian Flag Colors
export const SyrianFlagGradient: StoryObj<typeof GradientText> = {
  args: {
    children: 'الجمهورية العربية السورية',
    colors: ['#007A3D', '#FFFFFF', '#CE1126'],
    direction: 'horizontal',
    duration: 'ceremonial',
  },
  parameters: {
    docs: {
      description: {
        story: 'Gradient text using Syrian flag colors with Arabic text.'
      }
    }
  }
};

// Cultural Palette
export const CulturalGradient: StoryObj<typeof GradientText> = {
  args: {
    children: 'Beautiful Syrian Heritage',
    colors: ['var(--sid-forest-500)', 'var(--sid-wheat-400)', 'var(--sid-umber-500)'],
    direction: 'diagonal',
    duration: 'contemplative',
  },
  parameters: {
    docs: {
      description: {
        story: 'Diagonal gradient using Syrian cultural color palette.'
      }
    }
  }
};

// Forest to Wheat
export const ForestWheatGradient: StoryObj<typeof GradientText> = {
  args: {
    children: 'من الغابات إلى الحقول الذهبية',
    colors: ['var(--sid-forest-700)', 'var(--sid-forest-400)', 'var(--sid-wheat-400)', 'var(--sid-wheat-600)'],
    direction: 'vertical',
    duration: 'melodic',
  },
  parameters: {
    docs: {
      description: {
        story: 'Vertical gradient from forest green to golden wheat, representing Syrian landscapes.'
      }
    }
  }
};

// Morphing Text Stories
const MorphingTextMeta: Meta<typeof MorphingText> = {
  title: 'Animations/MorphingText',
  component: MorphingText,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
The MorphingText component smoothly transitions between different texts with cultural timing.
Perfect for rotating messages, multilingual content, and dynamic displays.

**Features:**
- Smooth text transitions
- Cultural timing (contemplative, ceremonial)
- Multilingual support (Arabic/English)
- Loop functionality
- Graceful animations
        `
      }
    }
  },
  decorators: [
    (Story) => (
      <AnimationProvider>
        <div style={{ 
          padding: '2rem', 
          fontSize: '1.5rem', 
          fontWeight: 'bold',
          minHeight: '100px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Story />
        </div>
      </AnimationProvider>
    ),
  ],
  tags: ['autodocs'],
};

// Multilingual Welcome
export const MultilingualWelcome: StoryObj<typeof MorphingText> = {
  args: {
    texts: [
      'مرحباً بكم',
      'Welcome',
      'أهلاً وسهلاً',
      'Bienvenue',
      'Bienvenidos',
      'Willkommen'
    ],
    displayDuration: 'contemplative',
    morphDuration: 'comfortable',
    loop: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Multilingual welcome message with smooth morphing transitions.'
      }
    }
  }
};

// Syrian Cities
export const SyrianCities: StoryObj<typeof MorphingText> = {
  args: {
    texts: [
      'دمشق الفيحاء',
      'حلب الشهباء', 
      'حمص الأبية',
      'حماة النواعير',
      'اللاذقية الزرقاء',
      'درعا البلد'
    ],
    displayDuration: 'ceremonial',
    morphDuration: 'graceful',
    loop: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Rotating display of Syrian cities with their traditional epithets.'
      }
    }
  }
};

// Cultural Values
export const CulturalValues: StoryObj<typeof MorphingText> = {
  args: {
    texts: [
      'الكرم والضيافة',
      'Generosity & Hospitality',
      'التراث والأصالة', 
      'Heritage & Authenticity',
      'الوحدة والتضامن',
      'Unity & Solidarity'
    ],
    displayDuration: 'contemplative',
    morphDuration: 'serene',
    loop: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Bilingual display of Syrian cultural values with serene transitions.'
      }
    }
  }
};

// Combined Animation Example
export const CombinedAnimations: Story = {
  render: () => (
    <AnimationProvider>
      <div style={{ 
        padding: '3rem', 
        textAlign: 'center',
        maxWidth: '800px',
        margin: '0 auto'
      }}>
        <div style={{ marginBottom: '2rem' }}>
          <GradientText 
            colors={['#007A3D', '#FFFFFF', '#CE1126']}
            duration="ceremonial"
          >
            <h1 style={{ fontSize: '2.5rem', margin: 0 }}>
              الهوية السورية
            </h1>
          </GradientText>
        </div>
        
        <div style={{ marginBottom: '2rem', fontSize: '1.2rem' }}>
          <MorphingText 
            texts={[
              'تراث عريق وحضارة أصيلة',
              'Rich Heritage & Authentic Civilization',
              'من أرض الشام الجميلة',
              'From the Beautiful Land of Sham'
            ]}
            displayDuration="contemplative"
            morphDuration="graceful"
          />
        </div>
        
        <div style={{ fontSize: '1rem', color: 'var(--sid-charcoal-600)' }}>
          <Typewriter 
            text="مرحباً بكم في مكتبة المكونات السورية - حيث التقنية تلتقي بالثقافة"
            speed={100}
            showCursor={true}
          />
        </div>
      </div>
    </AnimationProvider>
  ),
  parameters: {
    docs: {
      description: {
        story: 'A complete example combining all animation components to create a cultural presentation.'
      }
    }
  }
};
