/**
 * Syrian Identity Background Animation Components Stories
 * 
 * Showcasing beautiful animated backgrounds inspired by Syrian culture and Islamic art.
 */

import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { 
  AnimationProvider,
  AnimatedGeometricPattern,
  ParticleSystem,
  AnimatedGradient
} from '@sid/components';

// Main meta export for Storybook
const meta: Meta<typeof AnimatedGeometricPattern> = {
  title: 'Animations/Background/AnimatedGeometricPattern',
  component: AnimatedGeometricPattern,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
The AnimatedGeometricPattern component creates beautiful animated geometric patterns inspired by Islamic art and Syrian architecture.

**Cultural Context:**
- Damascus pattern: Inspired by traditional Damascus metalwork
- Star pattern: Based on Islamic 8-pointed star (Khatam)
- Hexagon pattern: Geometric harmony in Islamic art
- Arabesque pattern: Flowing organic forms
- Calligraphy pattern: Arabic script-inspired movements

**Features:**
- Multiple Islamic art-inspired patterns
- Cultural timing and easing
- RTL-aware animations
- Accessibility compliant (respects reduced motion)
- Customizable colors and opacity
        `
      }
    }
  },
  decorators: [
    (Story) => (
      <AnimationProvider>
        <div style={{ 
          position: 'relative',
          height: '400px',
          backgroundColor: 'var(--sid-charcoal-50)',
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Story />
          <div style={{
            position: 'relative',
            zIndex: 1,
            padding: '2rem',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: '8px',
            textAlign: 'center',
            backdropFilter: 'blur(10px)'
          }}>
            <h3 style={{ margin: '0 0 1rem 0', color: 'var(--sid-charcoal-900)' }}>
              نمط هندسي متحرك
            </h3>
            <p style={{ margin: 0, color: 'var(--sid-charcoal-600)' }}>
              Animated Geometric Pattern
            </p>
          </div>
        </div>
      </AnimationProvider>
    ),
  ],
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Damascus Pattern
export const DamascusPattern: StoryObj<typeof AnimatedGeometricPattern> = {
  args: {
    pattern: 'damascus',
    color: 'var(--sid-wheat-400)',
    secondaryColor: 'var(--sid-forest-300)',
    speed: 'contemplative',
    opacity: 0.15,
    size: 'md',
    fixed: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Damascus-inspired geometric pattern with traditional Syrian metalwork motifs.'
      }
    }
  }
};

// Star Pattern
export const StarPattern: StoryObj<typeof AnimatedGeometricPattern> = {
  args: {
    pattern: 'star',
    color: '#007A3D',
    secondaryColor: '#CE1126',
    speed: 'ceremonial',
    opacity: 0.2,
    size: 'lg',
    fixed: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Islamic 8-pointed star pattern using Syrian flag colors.'
      }
    }
  }
};

// Calligraphy Pattern
export const CalligraphyPattern: StoryObj<typeof AnimatedGeometricPattern> = {
  args: {
    pattern: 'calligraphy',
    color: 'var(--sid-umber-500)',
    secondaryColor: 'var(--sid-wheat-600)',
    speed: 'contemplative',
    opacity: 0.12,
    size: 'md',
    fixed: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Arabic calligraphy-inspired flowing patterns with cultural colors.'
      }
    }
  }
};



// Particle System Example (simplified for this story file)
export const ParticleSystemExample: Story = {
  render: () => (
    <AnimationProvider>
      <div style={{
        position: 'relative',
        height: '400px',
        backgroundColor: 'var(--sid-charcoal-900)',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <ParticleSystem
          colors={['#007A3D', '#FFFFFF', '#CE1126']}
          count={30}
          movement="cultural"
          speed="contemplative"
          opacity={0.8}
          sizeRange={[3, 6]}
          fixed={false}
        />
        <div style={{
          position: 'relative',
          zIndex: 1,
          padding: '2rem',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          borderRadius: '8px',
          textAlign: 'center',
          backdropFilter: 'blur(10px)'
        }}>
          <h3 style={{ margin: '0 0 1rem 0', color: 'white' }}>
            نظام الجسيمات
          </h3>
          <p style={{ margin: 0, color: 'rgba(255, 255, 255, 0.8)' }}>
            Particle System with Syrian Flag Colors
          </p>
        </div>
      </div>
    </AnimationProvider>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Floating particles in Syrian flag colors with cultural movement patterns.'
      }
    }
  }
};

// Animated Gradient Example
export const AnimatedGradientExample: Story = {
  render: () => (
    <AnimationProvider>
      <div style={{
        position: 'relative',
        height: '400px',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <AnimatedGradient
          colors={[
            'var(--sid-forest-700)',
            'var(--sid-forest-400)',
            'var(--sid-wheat-400)',
            'var(--sid-wheat-600)',
            'var(--sid-umber-400)'
          ]}
          direction="diagonal"
          speed="contemplative"
          size="400%"
          opacity={0.85}
          fixed={false}
        />
        <div style={{
          position: 'relative',
          zIndex: 1,
          padding: '2rem',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '8px',
          textAlign: 'center',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}>
          <h3 style={{ margin: '0 0 1rem 0', color: 'white', textShadow: '0 2px 4px rgba(0,0,0,0.5)' }}>
            تدرج متحرك
          </h3>
          <p style={{ margin: 0, color: 'rgba(255, 255, 255, 0.9)', textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}>
            Syrian Landscape Gradient
          </p>
        </div>
      </div>
    </AnimationProvider>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Animated gradient representing Syrian landscapes from forests to wheat fields.'
      }
    }
  }
};

// Combined Background Showcase
export const CombinedShowcase: Story = {
  render: () => (
    <AnimationProvider>
      <div style={{
        position: 'relative',
        height: '500px',
        overflow: 'hidden',
        backgroundColor: 'var(--sid-charcoal-900)'
      }}>
        {/* Animated Gradient Base */}
        <AnimatedGradient
          colors={[
            'var(--sid-forest-700)',
            'var(--sid-wheat-400)',
            'var(--sid-umber-500)',
            'var(--sid-charcoal-800)'
          ]}
          direction="diagonal"
          speed="contemplative"
          opacity={0.6}
          fixed={false}
        />

        {/* Geometric Pattern Overlay */}
        <AnimatedGeometricPattern
          pattern="damascus"
          color="var(--sid-wheat-200)"
          secondaryColor="var(--sid-forest-200)"
          speed="ceremonial"
          opacity={0.1}
          size="lg"
          fixed={false}
        />

        {/* Particle System */}
        <ParticleSystem
          colors={['#FFFFFF', 'var(--sid-wheat-300)', 'var(--sid-forest-300)']}
          count={30}
          movement="cultural"
          speed="contemplative"
          opacity={0.7}
          sizeRange={[2, 5]}
          fixed={false}
        />

        {/* Content Overlay */}
        <div style={{
          position: 'absolute',
          inset: 0,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1,
          textAlign: 'center',
          padding: '2rem'
        }}>
          <div style={{
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            padding: '3rem',
            borderRadius: '16px',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            maxWidth: '600px'
          }}>
            <h1 style={{
              margin: '0 0 1rem 0',
              color: 'white',
              fontSize: '2.5rem',
              fontWeight: 'bold',
              textShadow: '0 2px 8px rgba(0,0,0,0.5)'
            }}>
              الهوية البصرية السورية
            </h1>
            <h2 style={{
              margin: '0 0 2rem 0',
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: '1.5rem',
              fontWeight: 'normal',
              textShadow: '0 1px 4px rgba(0,0,0,0.5)'
            }}>
              Syrian Visual Identity
            </h2>
            <p style={{
              margin: 0,
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: '1.1rem',
              lineHeight: '1.6',
              textShadow: '0 1px 2px rgba(0,0,0,0.5)'
            }}>
              تجربة بصرية متكاملة تجمع بين التراث السوري الأصيل والتقنيات الحديثة
              <br />
              <em>A complete visual experience combining authentic Syrian heritage with modern technology</em>
            </p>
          </div>
        </div>
      </div>
    </AnimationProvider>
  ),
  parameters: {
    docs: {
      description: {
        story: 'A complete showcase combining all background animation components to create an immersive Syrian cultural experience.'
      }
    }
  }
};
