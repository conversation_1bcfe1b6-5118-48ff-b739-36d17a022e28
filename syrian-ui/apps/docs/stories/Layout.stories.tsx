/**
 * Syrian Identity Layout Components - Storybook Stories
 * 
 * Comprehensive examples showcasing:
 * - Arabic and English layout components
 * - RTL/LTR support with proper alignment
 * - Cultural color variants inspired by Syrian heritage
 * - All interaction states and accessibility features
 * - Beautiful Damascus-inspired styling
 */

import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from '@sid/components';

// Mock icons for examples
const StarIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M8 1l2 4h4l-3 3 1 4-4-2-4 2 1-4-3-3h4l2-4z"/>
  </svg>
);

const HeartIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M8 14s-6-4.5-6-8.5C2 3.5 4 2 6 2c1 0 2 .5 2 1.5C8 2.5 9 2 10 2c2 0 4 1.5 4 3.5C14 9.5 8 14 8 14z"/>
  </svg>
);

const CheckIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M13.5 3.5L6 11 2.5 7.5l1-1L6 9l6.5-6.5 1 1z"/>
  </svg>
);

const meta: Meta<typeof Card> = {
  title: 'Components/Layout',
  component: Card,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# Syrian Identity Layout Components

Beautiful layout components designed for Syrian cultural context with RTL-first approach.

## Components
- **Card**: Content containers with Damascus-inspired styling
- **Badge**: Status indicators with cultural color variants
- **Alert**: Notification components with proper RTL layout

## Features
- **RTL/LTR Support**: Seamless Arabic/English layout components
- **Cultural Variants**: Special variants with Syrian wheat and forest colors
- **Accessibility**: WCAG AA compliant with proper ARIA attributes
- **Interactive States**: Hover effects and smooth animations
- **Flexible Layout**: Adaptable to various content types

## Cultural Design
- **Default**: Clean modern design with Syrian forest green accents
- **Cultural**: Golden wheat colors celebrating Syrian agriculture
- **Elevated**: Enhanced shadows inspired by Damascus architecture
- **Outlined**: Bold borders with cultural authenticity
        `
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof Card>;

// === CARD STORIES ===

export const CardBasic: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1.5rem', flexWrap: 'wrap' }}>
      <Card style={{ width: '300px' }}>
        <h3 style={{ margin: '0 0 1rem 0', fontFamily: 'var(--sid-font-arabic)' }}>
          بطاقة أساسية
        </h3>
        <p style={{ margin: 0, color: 'var(--sid-text-secondary)' }}>
          هذه بطاقة أساسية تحتوي على محتوى نصي باللغة العربية مع تصميم سوري أصيل.
        </p>
      </Card>
      
      <Card variant="cultural" style={{ width: '300px' }}>
        <h3 style={{ margin: '0 0 1rem 0', fontFamily: 'var(--sid-font-arabic)' }}>
          بطاقة ثقافية
        </h3>
        <p style={{ margin: 0 }}>
          بطاقة بألوان القمح السوري التراثية مع خلفية دافئة تعكس الهوية الثقافية.
        </p>
      </Card>
    </div>
  )
};

export const CardVariants: Story = {
  render: () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', gap: '1.5rem', maxWidth: '1200px' }}>
      <Card variant="default" padding="lg">
        <h4 style={{ margin: '0 0 0.5rem 0', fontFamily: 'var(--sid-font-arabic)' }}>افتراضي</h4>
        <p style={{ margin: 0, fontSize: 'var(--sid-text-sm)', color: 'var(--sid-text-secondary)' }}>
          التصميم الافتراضي مع ظلال خفيفة
        </p>
      </Card>
      
      <Card variant="cultural" padding="lg">
        <h4 style={{ margin: '0 0 0.5rem 0', fontFamily: 'var(--sid-font-arabic)' }}>ثقافي</h4>
        <p style={{ margin: 0, fontSize: 'var(--sid-text-sm)' }}>
          ألوان القمح السوري التراثية
        </p>
      </Card>
      
      <Card variant="elevated" padding="lg">
        <h4 style={{ margin: '0 0 0.5rem 0', fontFamily: 'var(--sid-font-arabic)' }}>مرتفع</h4>
        <p style={{ margin: 0, fontSize: 'var(--sid-text-sm)', color: 'var(--sid-text-secondary)' }}>
          ظلال عميقة مستوحاة من العمارة الدمشقية
        </p>
      </Card>
      
      <Card variant="outlined" padding="lg">
        <h4 style={{ margin: '0 0 0.5rem 0', fontFamily: 'var(--sid-font-arabic)' }}>محدد</h4>
        <p style={{ margin: 0, fontSize: 'var(--sid-text-sm)', color: 'var(--sid-text-secondary)' }}>
          حدود واضحة بدون خلفية
        </p>
      </Card>
      
      <Card variant="filled" padding="lg">
        <h4 style={{ margin: '0 0 0.5rem 0', fontFamily: 'var(--sid-font-arabic)' }}>مملوء</h4>
        <p style={{ margin: 0, fontSize: 'var(--sid-text-sm)', color: 'var(--sid-text-secondary)' }}>
          خلفية ملونة بدون حدود
        </p>
      </Card>
    </div>
  )
};

export const CardWithHeaderFooter: Story = {
  render: () => (
    <Card 
      variant="cultural"
      style={{ width: '400px' }}
      header={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h3 style={{ margin: 0, fontFamily: 'var(--sid-font-arabic)' }}>مقال سوري</h3>
          <Badge variant="cultural" size="sm">جديد</Badge>
        </div>
      }
      footer={
        <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'flex-end' }}>
          <Button variant="outline" size="sm">مشاركة</Button>
          <Button variant="primary" size="sm">قراءة المزيد</Button>
        </div>
      }
    >
      <p style={{ margin: 0, lineHeight: 'var(--sid-leading-relaxed)' }}>
        هذا مثال على بطاقة مع رأس وتذييل، تحتوي على محتوى نصي باللغة العربية 
        مع تصميم يعكس الهوية السورية الأصيلة والتراث الثقافي العريق.
      </p>
    </Card>
  )
};

export const InteractiveCards: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1.5rem', flexWrap: 'wrap' }}>
      <Card 
        variant="elevated" 
        interactive 
        style={{ width: '280px', cursor: 'pointer' }}
        onClick={() => alert('تم النقر على البطاقة!')}
      >
        <div style={{ textAlign: 'center', padding: '1rem' }}>
          <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🏛️</div>
          <h4 style={{ margin: '0 0 0.5rem 0', fontFamily: 'var(--sid-font-arabic)' }}>
            التراث السوري
          </h4>
          <p style={{ margin: 0, fontSize: 'var(--sid-text-sm)', color: 'var(--sid-text-secondary)' }}>
            اكتشف جمال التراث والثقافة السورية
          </p>
        </div>
      </Card>
      
      <Card 
        variant="cultural" 
        interactive 
        style={{ width: '280px', cursor: 'pointer' }}
        onClick={() => alert('مرحباً بك!')}
      >
        <div style={{ textAlign: 'center', padding: '1rem' }}>
          <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🌾</div>
          <h4 style={{ margin: '0 0 0.5rem 0', fontFamily: 'var(--sid-font-arabic)' }}>
            الزراعة السورية
          </h4>
          <p style={{ margin: 0, fontSize: 'var(--sid-text-sm)' }}>
            القمح الذهبي والأراضي الخصبة
          </p>
        </div>
      </Card>
    </div>
  )
};

// === BADGE STORIES ===

export const BadgeBasic: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
      <Badge>افتراضي</Badge>
      <Badge variant="cultural">ثقافي</Badge>
      <Badge variant="success">نجاح</Badge>
      <Badge variant="warning">تحذير</Badge>
      <Badge variant="error">خطأ</Badge>
      <Badge variant="info">معلومات</Badge>
    </div>
  )
};

export const BadgeSizes: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
      <Badge size="sm" variant="cultural">صغير</Badge>
      <Badge size="md" variant="cultural">متوسط</Badge>
      <Badge size="lg" variant="cultural">كبير</Badge>
    </div>
  )
};

export const BadgeShapes: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
      <Badge shape="rounded" variant="cultural">مدور</Badge>
      <Badge shape="pill" variant="cultural">حبة دواء</Badge>
      <Badge shape="square" variant="cultural">مربع</Badge>
    </div>
  )
};

export const BadgeWithIcons: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
      <Badge variant="success" startIcon={<CheckIcon />}>مكتمل</Badge>
      <Badge variant="cultural" startIcon={<StarIcon />}>مميز</Badge>
      <Badge variant="error" endIcon={<HeartIcon />}>محبوب</Badge>
      <Badge variant="info" dot>5</Badge>
    </div>
  )
};

// === ALERT STORIES ===

export const AlertBasic: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', maxWidth: '600px' }}>
      <Alert variant="info" title="معلومات مهمة">
        هذا تنبيه معلوماتي يحتوي على معلومات مهمة للمستخدم.
      </Alert>
      
      <Alert variant="cultural" title="رسالة ثقافية" dismissible>
        مرحباً بك في مكتبة الهوية السورية للمكونات. نحن نحتفي بالتراث السوري الأصيل.
      </Alert>
    </div>
  )
};

export const AlertVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', maxWidth: '600px' }}>
      <Alert variant="info" title="معلومات">
        هذا تنبيه معلوماتي باللون الأزرق التقليدي.
      </Alert>
      
      <Alert variant="success" title="تم بنجاح">
        تم إكمال العملية بنجاح باستخدام الألوان الخضراء.
      </Alert>
      
      <Alert variant="warning" title="تحذير">
        يرجى الانتباه لهذا التحذير المهم باللون الأصفر.
      </Alert>
      
      <Alert variant="error" title="خطأ">
        حدث خطأ يتطلب انتباهك الفوري باللون الأحمر.
      </Alert>
      
      <Alert variant="cultural" title="رسالة ثقافية">
        رسالة بألوان القمح السوري التراثية الدافئة.
      </Alert>
    </div>
  )
};

export const AlertWithActions: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', maxWidth: '600px' }}>
      <Alert 
        variant="cultural" 
        title="تحديث متوفر"
        dismissible
        actions={
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <Button variant="outline" size="sm">لاحقاً</Button>
            <Button variant="primary" size="sm">تحديث الآن</Button>
          </div>
        }
      >
        يتوفر إصدار جديد من التطبيق مع ميزات محسنة وإصلاحات أمنية.
      </Alert>
      
      <Alert 
        variant="success" 
        title="تم الحفظ"
        actions={
          <Button variant="ghost" size="sm">عرض التفاصيل</Button>
        }
      >
        تم حفظ التغييرات بنجاح في قاعدة البيانات.
      </Alert>
    </div>
  )
};
