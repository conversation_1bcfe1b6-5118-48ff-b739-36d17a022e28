/**
 * Syrian Identity Input Components - Storybook Stories
 * 
 * Comprehensive examples showcasing:
 * - Arabic and English text input
 * - RTL/LTR support with proper text alignment
 * - Cultural color variants inspired by Syrian heritage
 * - All validation states and accessibility features
 * - Beautiful Damascus-inspired focus states
 */

import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Input, Textarea, Select } from '@sid/components';

// Mock icons for examples
const UserIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M8 8a3 3 0 100-6 3 3 0 000 6zM8 9a5 5 0 00-5 5v1h10v-1a5 5 0 00-5-5z"/>
  </svg>
);

const EmailIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M2 3h12l-6 4-6-4zm0 2v8h12V5l-6 4-6-4z"/>
  </svg>
);

const LocationIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M8 2a4 4 0 00-4 4c0 3 4 8 4 8s4-5 4-8a4 4 0 00-4-4zm0 5.5a1.5 1.5 0 110-3 1.5 1.5 0 010 3z"/>
  </svg>
);

// Syrian provinces for Select examples
const syrianProvinces = [
  { value: 'damascus', label: 'دمشق' },
  { value: 'aleppo', label: 'حلب' },
  { value: 'homs', label: 'حمص' },
  { value: 'hama', label: 'حماة' },
  { value: 'lattakia', label: 'اللاذقية' },
  { value: 'tartus', label: 'طرطوس' },
  { value: 'daraa', label: 'درعا' },
  { value: 'deir-ez-zor', label: 'دير الزور' },
  { value: 'raqqa', label: 'الرقة' },
  { value: 'idlib', label: 'إدلب' },
  { value: 'quneitra', label: 'القنيطرة' },
  { value: 'as-suwayda', label: 'السويداء' },
  { value: 'rif-dimashq', label: 'ريف دمشق' },
  { value: 'hasaka', label: 'الحسكة' }
];

const meta: Meta<typeof Input> = {
  title: 'Components/Input',
  component: Input,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# Syrian Identity Input Components

Beautiful form input components designed for Syrian cultural context with RTL-first approach.

## Features
- **RTL/LTR Support**: Seamless Arabic/English text input with proper alignment
- **Cultural Variants**: Special "cultural" variant with Syrian wheat colors
- **Arabic Typography**: Optimized for Arabic text with proper font rendering
- **Accessibility**: WCAG AA compliant with screen reader support
- **Validation States**: Error, success, warning states with cultural colors
- **Icons**: Start/end icon support with RTL-aware positioning

## Cultural Design
- **Default**: Clean modern design with Syrian forest green accents
- **Filled**: Subtle background with Syrian charcoal colors
- **Outlined**: Bold borders inspired by Damascus architecture
- **Cultural**: Golden wheat background celebrating Syrian agriculture
        `
      }
    }
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'filled', 'outlined', 'cultural'],
      description: 'Visual style variant inspired by Syrian design'
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size scale for different use cases'
    },
    state: {
      control: 'select',
      options: ['default', 'error', 'success', 'warning'],
      description: 'Validation state with cultural colors'
    },
    fullWidth: {
      control: 'boolean',
      description: 'Makes input take full width of container'
    },
    dir: {
      control: 'select',
      options: ['auto', 'rtl', 'ltr'],
      description: 'Text direction override'
    }
  },
  args: {
    label: 'الاسم الكامل',
    placeholder: 'أدخل اسمك الكامل',
    variant: 'default',
    size: 'md',
    dir: 'auto'
  }
};

export default meta;
type Story = StoryObj<typeof Input>;

// === INPUT STORIES ===

export const ArabicInput: Story = {
  args: {
    label: 'الاسم الكامل',
    placeholder: 'أدخل اسمك الكامل',
    variant: 'cultural'
  }
};

export const EnglishInput: Story = {
  args: {
    label: 'Full Name',
    placeholder: 'Enter your full name',
    variant: 'default',
    dir: 'ltr'
  }
};

export const AllVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', minWidth: '300px' }}>
      <Input variant="default" label="افتراضي" placeholder="النمط الافتراضي" />
      <Input variant="filled" label="مملوء" placeholder="النمط المملوء" />
      <Input variant="outlined" label="محدد" placeholder="النمط المحدد" />
      <Input variant="cultural" label="ثقافي" placeholder="النمط الثقافي السوري" />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All input variants showcasing Syrian cultural design language.'
      }
    }
  }
};

export const AllSizes: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', minWidth: '300px' }}>
      <Input size="sm" label="صغير" placeholder="حجم صغير" />
      <Input size="md" label="متوسط" placeholder="حجم متوسط" />
      <Input size="lg" label="كبير" placeholder="حجم كبير" />
    </div>
  )
};

export const ValidationStates: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', minWidth: '300px' }}>
      <Input 
        label="حالة عادية" 
        placeholder="إدخال عادي"
        helperText="هذا نص مساعد"
      />
      <Input 
        label="حالة خطأ" 
        placeholder="إدخال خاطئ"
        state="error"
        errorMessage="هذا الحقل مطلوب"
      />
      <Input 
        label="حالة نجاح" 
        placeholder="إدخال صحيح"
        state="success"
        helperText="تم التحقق بنجاح"
      />
      <Input 
        label="حالة تحذير" 
        placeholder="إدخال محذر"
        state="warning"
        helperText="يرجى التحقق من البيانات"
      />
    </div>
  )
};

export const WithIcons: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', minWidth: '300px' }}>
      <Input
        label="اسم المستخدم"
        placeholder="أدخل اسم المستخدم"
        startIcon={<UserIcon />}
        variant="cultural"
      />
      <Input
        label="البريد الإلكتروني"
        placeholder="أدخل بريدك الإلكتروني"
        startIcon={<EmailIcon />}
        type="email"
      />
      <Input
        label="الموقع"
        placeholder="أدخل موقعك"
        endIcon={<LocationIcon />}
      />
    </div>
  )
};

export const RTLPlaceholderAlignment: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem', minWidth: '400px' }}>
      <div>
        <h4 style={{ fontFamily: 'var(--sid-font-arabic)', marginBottom: '1rem', color: 'var(--sid-text-primary)' }}>
          محاذاة النص التلقائية (طبيعية)
        </h4>
        <p style={{ fontSize: 'var(--sid-text-sm)', color: 'var(--sid-text-secondary)', marginBottom: '1rem' }}>
          النص المساعد يبدأ من اليمين مثل النص المكتوب - الطريقة المعتادة
        </p>
        <Input
          label="الاسم الكامل"
          placeholder="أدخل اسمك الكامل هنا"
          variant="cultural"
          dir="rtl"
          placeholderAlign="natural"
          fullWidth
        />
      </div>

      <div>
        <h4 style={{ fontFamily: 'var(--sid-font-arabic)', marginBottom: '1rem', color: 'var(--sid-text-primary)' }}>
          محاذاة النص العكسية
        </h4>
        <p style={{ fontSize: 'var(--sid-text-sm)', color: 'var(--sid-text-secondary)', marginBottom: '1rem' }}>
          النص المساعد يبدأ من اليسار ليوضح مكان انتهاء النص المكتوب
        </p>
        <Input
          label="الاسم الكامل"
          placeholder="أدخل اسمك الكامل هنا"
          variant="outlined"
          dir="rtl"
          placeholderAlign="opposite"
          fullWidth
        />
      </div>

      <div>
        <h4 style={{ fontFamily: 'var(--sid-font-arabic)', marginBottom: '1rem', color: 'var(--sid-text-primary)' }}>
          الكشف التلقائي للاتجاه
        </h4>
        <p style={{ fontSize: 'var(--sid-text-sm)', color: 'var(--sid-text-secondary)', marginBottom: '1rem' }}>
          يتم تحديد اتجاه النص تلقائياً بناءً على محتوى النص المساعد
        </p>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <Input
            label="Auto-detected Arabic"
            placeholder="أدخل النص العربي"
            dir="auto"
            variant="filled"
            fullWidth
          />
          <Input
            label="Auto-detected English"
            placeholder="Enter English text"
            dir="auto"
            variant="filled"
            fullWidth
          />
        </div>
      </div>

      <div style={{
        padding: '1rem',
        backgroundColor: 'var(--sid-wheat-100)',
        borderRadius: 'var(--sid-radius-card)',
        borderInlineStart: '4px solid var(--sid-wheat-600)'
      }}>
        <h5 style={{ fontFamily: 'var(--sid-font-arabic)', margin: '0 0 0.5rem 0', color: 'var(--sid-wheat-800)' }}>
          💡 نصيحة تجربة المستخدم
        </h5>
        <p style={{ fontSize: 'var(--sid-text-sm)', color: 'var(--sid-wheat-700)', margin: 0 }}>
          المحاذاة الطبيعية أكثر شيوعاً في التطبيقات العربية، بينما المحاذاة العكسية تساعد المستخدم على فهم مكان انتهاء النص. الكشف التلقائي يوفر تجربة سلسة للمحتوى المختلط.
        </p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Comprehensive RTL placeholder alignment examples including auto-detection, natural alignment, and opposite alignment strategies for optimal Arabic UX.'
      }
    }
  }
};

export const RTLValidationStates: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', minWidth: '400px' }}>
      <h3 style={{
        fontFamily: 'var(--sid-font-arabic)',
        textAlign: 'center',
        marginBottom: '1rem',
        color: 'var(--sid-text-primary)'
      }}>
        حالات التحقق في النصوص العربية
      </h3>

      <Input
        label="حالة عادية"
        placeholder="أدخل النص هنا"
        helperText="هذا نص مساعد للإرشاد"
        dir="rtl"
        variant="cultural"
        fullWidth
      />

      <Input
        label="حالة خطأ"
        placeholder="أدخل البريد الإلكتروني"
        state="error"
        errorMessage="البريد الإلكتروني غير صحيح"
        dir="rtl"
        fullWidth
      />

      <Input
        label="حالة نجاح"
        placeholder="أدخل كلمة المرور"
        state="success"
        helperText="كلمة المرور قوية ومقبولة"
        dir="rtl"
        type="password"
        fullWidth
      />

      <Input
        label="حالة تحذير"
        placeholder="أدخل رقم الهاتف"
        state="warning"
        helperText="تأكد من صحة رقم الهاتف"
        dir="rtl"
        fullWidth
      />

      <div style={{ display: 'flex', gap: '1rem' }}>
        <Input
          label="مع أيقونة البداية"
          placeholder="اسم المستخدم"
          startIcon={<UserIcon />}
          dir="rtl"
          variant="outlined"
          style={{ flex: 1 }}
        />
        <Input
          label="مع أيقونة النهاية"
          placeholder="الموقع"
          endIcon={<LocationIcon />}
          dir="rtl"
          variant="outlined"
          style={{ flex: 1 }}
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'RTL validation states showing proper Arabic text alignment, error messages, and icon positioning in right-to-left context.'
      }
    }
  }
};

export const MixedDirectionInputs: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem', minWidth: '400px' }}>
      <div>
        <h4 style={{ marginBottom: '1rem', color: 'var(--sid-text-primary)' }}>
          Mixed Content Auto-Detection
        </h4>
        <p style={{ fontSize: 'var(--sid-text-sm)', color: 'var(--sid-text-secondary)', marginBottom: '1rem' }}>
          Inputs automatically detect text direction based on placeholder content
        </p>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <Input
            label="Arabic Content"
            placeholder="البحث في المنتجات"
            dir="auto"
            variant="filled"
            fullWidth
          />
          <Input
            label="English Content"
            placeholder="Search products"
            dir="auto"
            variant="filled"
            fullWidth
          />
          <Input
            label="Mixed Content (Arabic dominant)"
            placeholder="البحث Search المنتجات"
            dir="auto"
            variant="filled"
            fullWidth
          />
          <Input
            label="Mixed Content (English dominant)"
            placeholder="Search البحث products"
            dir="auto"
            variant="filled"
            fullWidth
          />
        </div>
      </div>

      <div style={{
        padding: '1rem',
        backgroundColor: 'var(--sid-forest-50)',
        borderRadius: 'var(--sid-radius-card)',
        borderInlineStart: '4px solid var(--sid-forest-500)'
      }}>
        <h5 style={{ margin: '0 0 0.5rem 0', color: 'var(--sid-forest-800)' }}>
          🔍 Auto-Detection Logic
        </h5>
        <p style={{ fontSize: 'var(--sid-text-sm)', color: 'var(--sid-forest-700)', margin: 0 }}>
          The component analyzes Unicode characters in placeholder text to determine direction.
          RTL characters (Arabic, Hebrew) take precedence when present in equal or greater numbers than LTR characters.
        </p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates automatic text direction detection for mixed content scenarios, showing how the component handles Arabic, English, and mixed text inputs.'
      }
    }
  }
};

// === TEXTAREA STORIES ===

export const TextareaBasic: Story = {
  render: () => (
    <Textarea
      label="رسالتك"
      placeholder="اكتب رسالتك هنا..."
      variant="cultural"
      rows={4}
    />
  )
};

export const TextareaAutoResize: Story = {
  render: () => (
    <Textarea
      label="نص قابل للتوسع"
      placeholder="ابدأ الكتابة وسيتوسع النص تلقائياً..."
      autoResize
      minRows={3}
      maxRows={8}
      helperText="سيتوسع النص تلقائياً حسب المحتوى"
    />
  )
};

// === SELECT STORIES ===

export const SelectBasic: Story = {
  render: () => (
    <Select
      label="المحافظة"
      placeholder="اختر المحافظة"
      options={syrianProvinces}
      variant="cultural"
    />
  )
};

export const SelectWithValidation: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', minWidth: '300px' }}>
      <Select
        label="المحافظة المطلوبة"
        placeholder="اختر المحافظة"
        options={syrianProvinces}
        state="error"
        errorMessage="يرجى اختيار المحافظة"
      />
      <Select
        label="المحافظة المختارة"
        options={syrianProvinces}
        value="damascus"
        state="success"
        helperText="تم الاختيار بنجاح"
      />
    </div>
  )
};

// === FORM EXAMPLE ===

export const SyrianContactForm: Story = {
  render: () => (
    <div style={{ 
      maxWidth: '400px', 
      padding: '2rem', 
      backgroundColor: 'var(--sid-bg-secondary)', 
      borderRadius: 'var(--sid-radius-card)',
      boxShadow: 'var(--sid-shadow-card)'
    }}>
      <h3 style={{ 
        fontFamily: 'var(--sid-font-arabic)', 
        textAlign: 'center', 
        marginBottom: '1.5rem',
        color: 'var(--sid-text-primary)'
      }}>
        نموذج التواصل
      </h3>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
        <Input
          label="الاسم الكامل"
          placeholder="أدخل اسمك الكامل"
          startIcon={<UserIcon />}
          variant="cultural"
          fullWidth
        />
        
        <Input
          label="البريد الإلكتروني"
          placeholder="أدخل بريدك الإلكتروني"
          type="email"
          startIcon={<EmailIcon />}
          fullWidth
        />
        
        <Select
          label="المحافظة"
          placeholder="اختر المحافظة"
          options={syrianProvinces}
          variant="cultural"
          fullWidth
        />
        
        <Textarea
          label="الرسالة"
          placeholder="اكتب رسالتك هنا..."
          variant="cultural"
          autoResize
          minRows={4}
          fullWidth
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Complete Syrian contact form showcasing all input components with cultural design.'
      }
    }
  }
};
