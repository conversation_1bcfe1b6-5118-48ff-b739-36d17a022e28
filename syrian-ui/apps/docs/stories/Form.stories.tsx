/**
 * Syrian Identity Form Components - Storybook Stories
 * 
 * Comprehensive examples showcasing:
 * - Arabic and English form controls
 * - RTL/LTR support with proper alignment
 * - Cultural color variants inspired by Syrian heritage
 * - All interaction states and accessibility features
 * - Beautiful Damascus-inspired animations
 */

import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Checkbox, Radio, RadioGroup, Switch } from '@sid/components';
import { useState } from 'react';

const meta: Meta<typeof Checkbox> = {
  title: 'Components/Form Controls',
  component: Checkbox,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# Syrian Identity Form Controls

Beautiful form control components designed for Syrian cultural context with RTL-first approach.

## Components
- **Checkbox**: Multi-selection with Damascus-inspired check marks
- **Radio**: Single selection with circular Syrian design
- **Switch**: Toggle controls with cultural color variants
- **RadioGroup**: Grouped radio options with proper spacing

## Features
- **RTL/LTR Support**: Seamless Arabic/English form controls
- **Cultural Variants**: Special variants with Syrian wheat and forest colors
- **Accessibility**: WCAG AA compliant with proper ARIA attributes
- **Touch Friendly**: Optimized sizing for mobile devices
- **Smooth Animations**: Damascus-inspired transitions and micro-interactions

## Cultural Design
- **Default**: Clean modern design with Syrian forest green
- **Cultural**: Golden wheat colors celebrating Syrian agriculture
- **Success**: Syrian flag green for positive actions
- **Warning**: Wheat tones for cautionary states
        `
      }
    }
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'cultural', 'success', 'warning'],
      description: 'Visual style variant inspired by Syrian design'
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size scale for different use cases'
    },
    dir: {
      control: 'select',
      options: ['auto', 'rtl', 'ltr'],
      description: 'Text direction override'
    }
  }
};

export default meta;
type Story = StoryObj<typeof Checkbox>;

// === CHECKBOX STORIES ===

export const CheckboxBasic: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <Checkbox label="أوافق على الشروط والأحكام" variant="cultural" />
      <Checkbox label="I agree to the terms and conditions" dir="ltr" />
    </div>
  )
};

export const CheckboxVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', minWidth: '300px' }}>
      <Checkbox 
        variant="default" 
        label="الخيار الافتراضي" 
        helperText="هذا نص مساعد للخيار الافتراضي"
        defaultChecked
      />
      <Checkbox 
        variant="cultural" 
        label="الخيار الثقافي السوري" 
        helperText="مستوحى من التراث السوري"
        defaultChecked
      />
      <Checkbox 
        variant="success" 
        label="خيار النجاح" 
        helperText="يستخدم للحالات الإيجابية"
        defaultChecked
      />
      <Checkbox 
        variant="warning" 
        label="خيار التحذير" 
        helperText="يستخدم للحالات التحذيرية"
        defaultChecked
      />
    </div>
  )
};

export const CheckboxStates: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <Checkbox label="غير محدد" variant="cultural" />
      <Checkbox label="محدد" variant="cultural" defaultChecked />
      <Checkbox label="غير محدد جزئياً" variant="cultural" indeterminate />
      <Checkbox label="معطل" variant="cultural" disabled />
      <Checkbox label="معطل ومحدد" variant="cultural" disabled defaultChecked />
    </div>
  )
};

// === RADIO STORIES ===

export const RadioBasic: Story = {
  render: () => {
    const [gender, setGender] = useState('');
    
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
        <h4 style={{ fontFamily: 'var(--sid-font-arabic)', margin: 0 }}>الجنس</h4>
        <Radio 
          name="gender" 
          value="male" 
          label="ذكر" 
          variant="cultural"
          checked={gender === 'male'}
          onChange={() => setGender('male')}
        />
        <Radio 
          name="gender" 
          value="female" 
          label="أنثى" 
          variant="cultural"
          checked={gender === 'female'}
          onChange={() => setGender('female')}
        />
      </div>
    );
  }
};

export const RadioGroupExample: Story = {
  render: () => {
    const [province, setProvince] = useState('');
    
    return (
      <div style={{ minWidth: '300px' }}>
        <h4 style={{ fontFamily: 'var(--sid-font-arabic)', marginBottom: '1rem' }}>
          اختر المحافظة
        </h4>
        <RadioGroup
          name="province"
          variant="cultural"
          value={province}
          onChange={setProvince}
          options={[
            { value: 'damascus', label: 'دمشق', helperText: 'العاصمة' },
            { value: 'aleppo', label: 'حلب', helperText: 'العاصمة الاقتصادية' },
            { value: 'homs', label: 'حمص', helperText: 'عاصمة الثورة' },
            { value: 'lattakia', label: 'اللاذقية', helperText: 'العروس الساحلية' }
          ]}
        />
      </div>
    );
  }
};

// === SWITCH STORIES ===

export const SwitchBasic: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', minWidth: '300px' }}>
      <Switch 
        label="تفعيل الإشعارات" 
        helperText="ستتلقى إشعارات عند وصول رسائل جديدة"
        variant="cultural"
      />
      <Switch 
        label="الوضع المظلم" 
        helperText="تغيير مظهر التطبيق إلى الوضع المظلم"
        defaultChecked
      />
    </div>
  )
};

export const SwitchVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', minWidth: '300px' }}>
      <Switch variant="default" label="الافتراضي" defaultChecked />
      <Switch variant="cultural" label="الثقافي السوري" defaultChecked />
      <Switch variant="success" label="النجاح" defaultChecked />
      <Switch variant="warning" label="التحذير" defaultChecked />
    </div>
  )
};

export const SwitchSizes: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', minWidth: '300px' }}>
      <Switch size="sm" label="صغير" variant="cultural" defaultChecked />
      <Switch size="md" label="متوسط" variant="cultural" defaultChecked />
      <Switch size="lg" label="كبير" variant="cultural" defaultChecked />
    </div>
  )
};

export const SwitchWithText: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', minWidth: '300px' }}>
      <Switch 
        label="مع نص داخلي" 
        showText 
        onText="تشغيل"
        offText="إيقاف"
        variant="cultural"
        defaultChecked
      />
      <Switch 
        label="English with text" 
        showText 
        onText="On"
        offText="Off"
        dir="ltr"
        size="lg"
      />
    </div>
  )
};

// === COMPREHENSIVE FORM EXAMPLE ===

export const SyrianSettingsForm: Story = {
  render: () => {
    const [notifications, setNotifications] = useState(true);
    const [darkMode, setDarkMode] = useState(false);
    const [language, setLanguage] = useState('arabic');
    const [newsletter, setNewsletter] = useState(false);
    const [privacy, setPrivacy] = useState(false);
    const [terms, setTerms] = useState(false);
    
    return (
      <div style={{ 
        maxWidth: '500px', 
        padding: '2rem', 
        backgroundColor: 'var(--sid-bg-secondary)', 
        borderRadius: 'var(--sid-radius-card)',
        boxShadow: 'var(--sid-shadow-card)'
      }}>
        <h3 style={{ 
          fontFamily: 'var(--sid-font-arabic)', 
          textAlign: 'center', 
          marginBottom: '2rem',
          color: 'var(--sid-text-primary)'
        }}>
          إعدادات التطبيق
        </h3>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
          {/* Switches Section */}
          <div>
            <h4 style={{ fontFamily: 'var(--sid-font-arabic)', marginBottom: '1rem', color: 'var(--sid-forest-700)' }}>
              الإعدادات العامة
            </h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              <Switch 
                label="تفعيل الإشعارات" 
                helperText="ستتلقى إشعارات عند وصول رسائل جديدة"
                variant="cultural"
                checked={notifications}
                onChange={(e) => setNotifications(e.target.checked)}
              />
              <Switch 
                label="الوضع المظلم" 
                helperText="تغيير مظهر التطبيق إلى الوضع المظلم"
                checked={darkMode}
                onChange={(e) => setDarkMode(e.target.checked)}
              />
            </div>
          </div>
          
          {/* Radio Group Section */}
          <div>
            <h4 style={{ fontFamily: 'var(--sid-font-arabic)', marginBottom: '1rem', color: 'var(--sid-forest-700)' }}>
              اللغة المفضلة
            </h4>
            <RadioGroup
              name="language"
              variant="cultural"
              value={language}
              onChange={setLanguage}
              options={[
                { value: 'arabic', label: 'العربية', helperText: 'اللغة الافتراضية' },
                { value: 'english', label: 'English', helperText: 'Secondary language' },
                { value: 'kurdish', label: 'کوردی', helperText: 'Kurdish language' }
              ]}
            />
          </div>
          
          {/* Checkboxes Section */}
          <div>
            <h4 style={{ fontFamily: 'var(--sid-font-arabic)', marginBottom: '1rem', color: 'var(--sid-forest-700)' }}>
              التفضيلات الإضافية
            </h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              <Checkbox 
                label="الاشتراك في النشرة الإخبارية" 
                helperText="ستتلقى أخبار ومستجدات التطبيق"
                variant="cultural"
                checked={newsletter}
                onChange={(e) => setNewsletter(e.target.checked)}
              />
              <Checkbox 
                label="أوافق على سياسة الخصوصية" 
                variant="success"
                checked={privacy}
                onChange={(e) => setPrivacy(e.target.checked)}
              />
              <Checkbox 
                label="أوافق على الشروط والأحكام" 
                variant="success"
                checked={terms}
                onChange={(e) => setTerms(e.target.checked)}
              />
            </div>
          </div>
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Complete Syrian settings form showcasing all form control components with cultural design and proper Arabic typography.'
      }
    }
  }
};
