# 🇸🇾 Syrian Identity UI

**A comprehensive React component library celebrating Syrian culture with RTL-first design, Arabic typography, and cultural authenticity.**

[![npm version](https://badge.fury.io/js/%40sid%2Fcomponents.svg)](https://www.npmjs.com/package/@sid/components)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-Ready-blue.svg)](https://www.typescriptlang.org/)
[![Storybook](https://img.shields.io/badge/Storybook-Ready-ff69b4.svg)](https://storybook.js.org/)

## ✨ Features

- **🎨 Syrian Cultural Design**: Authentic color palette inspired by Syrian landscapes, architecture, and heritage
- **📱 RTL-First Architecture**: Built with CSS logical properties for seamless Arabic/English support
- **🔤 Arabic Typography**: Optimized for Arabic text with Qomra font support and proper fallbacks
- **♿ Accessibility Excellence**: WCAG AA compliant with comprehensive screen reader support
- **⚡ Performance Optimized**: Tree-shakeable components with minimal runtime overhead
- **🛠 Developer Experience**: Full TypeScript support with comprehensive Storybook documentation
- **🌍 Cultural Context**: Every component respects Syrian visual identity and cultural nuances

## 🚀 Quick Start

```bash
# Install packages
npm install @sid/tokens @sid/components
# or
pnpm add @sid/tokens @sid/components
```

```tsx
import { Button } from '@sid/components';
import { injectSidTokens } from '@sid/tokens';

// Inject design tokens (do this once in your app)
injectSidTokens();

function App() {
  return (
    <div dir="rtl">
      <Button variant="primary">مرحباً بك</Button>
      <Button variant="secondary" dir="ltr">Welcome</Button>
    </div>
  );
}
```

## 🎨 Design System

### Color Palette
Our colors are inspired by Syrian cultural heritage:

- **🌲 Forest Green**: Syrian olive groves and landscapes (`--sid-forest-600`)
- **🌾 Golden Wheat**: Agricultural heritage and prosperity (`--sid-wheat-400`)
- **🏛 Deep Umber**: Damascus architecture and earth (`--sid-umber-500`)
- **✒️ Charcoal**: Traditional calligraphy and stone (`--sid-charcoal-900`)

### Typography
- **Primary**: Arabic-optimized stack with Qomra font support
- **Fallbacks**: Noto Kufi Arabic, Amiri, system fonts
- **Features**: Proper ligature support, optimized line heights for Arabic text

## 📚 Documentation

Visit our [Storybook documentation](http://localhost:6007) to explore:
- Interactive component playground
- Arabic and English examples
- RTL/LTR toggle functionality
- Accessibility testing tools
- Cultural context and usage guidelines

## 🏗 Architecture

This project is built with modern tools and best practices:

- **🏎 [Turborepo](https://turborepo.com)**: High-performance monorepo build system
- **⚛️ [React 18](https://reactjs.org/)**: Latest React with concurrent features
- **📦 [TypeScript](https://www.typescriptlang.org/)**: Full type safety and IntelliSense
- **📖 [Storybook](https://storybook.js.org/)**: Component documentation and testing
- **🛠 [tsup](https://github.com/egoist/tsup)**: Fast TypeScript bundler powered by esbuild
- [Changesets](https://github.com/changesets/changesets) for managing versioning and changelogs
- [GitHub Actions](https://github.com/changesets/action) for fully automated package publishing

## Using this example

Run the following command:

```sh
npx create-turbo@latest -e design-system
```

### 📦 Packages

| Package | Description | Version |
|---------|-------------|---------|
| `@sid/tokens` | Design tokens and CSS variables | ![npm](https://img.shields.io/npm/v/@sid/tokens) |
| `@sid/components` | React components library | ![npm](https://img.shields.io/npm/v/@sid/components) |
| `@sid/icons` | Syrian cultural icons | 🚧 Coming Soon |
| `@sid/patterns` | Islamic geometric patterns | 🚧 Coming Soon |

## 🛠 Development Commands

```bash
# Build all packages
pnpm build

# Start Storybook development server
pnpm dev

# Run linting across all packages
pnpm lint

# Clean all build artifacts
pnpm clean

# Generate changeset for releases
pnpm changeset
```

## 🧩 Components

### ✅ Available Components

| Component | Description | Status |
|-----------|-------------|--------|
| **Button** | Primary action component with 5 variants and RTL support | ✅ Production Ready |

### 🚧 Coming Soon

| Component | Description | Priority |
|-----------|-------------|----------|
| **Input** | Text input with Arabic text handling | High |
| **Card** | Content containers with Syrian styling | High |
| **Select** | Dropdown with Arabic option support | High |
| **Dialog** | Modal dialogs using Radix UI primitives | Medium |
| **Tooltip** | Hover information with RTL positioning | Medium |

## 🌍 Internationalization

Syrian Identity UI is built with internationalization at its core:

```tsx
// RTL (Arabic) - Default
<div dir="rtl">
  <Button>حفظ التغييرات</Button>
</div>

// LTR (English)
<div dir="ltr">
  <Button dir="ltr">Save Changes</Button>
</div>

// Auto-detection based on content
<Button dir="auto">مرحباً Hello</Button>
```

## ♿ Accessibility

All components follow WCAG AA guidelines:

- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Readers**: Proper ARIA attributes and semantic HTML
- **Focus Management**: Visible focus indicators and logical tab order
- **Color Contrast**: Minimum 4.5:1 contrast ratios
- **Touch Targets**: Minimum 44px touch targets on mobile

## 🎨 Customization

### Using Design Tokens

```tsx
import { sidColors, sidSpace } from '@sid/tokens';

const customButton = {
  backgroundColor: sidColors.forest[600],
  padding: `${sidSpace[2]} ${sidSpace[4]}`,
  borderRadius: sidSpace[1]
};
```

### CSS Custom Properties

```css
.my-component {
  background-color: var(--sid-forest-600);
  padding: var(--sid-space-2) var(--sid-space-4);
  border-radius: var(--sid-radius-button);
  font-family: var(--sid-font-arabic);
}
```

## 🤝 Contributing

We welcome contributions from the Syrian developer community and beyond! Please read our [Contributing Guide](./CONTRIBUTING.md) for details on:

- Code of conduct
- Development setup
- Submitting pull requests
- Cultural review process
- Component design guidelines

## 📄 License

MIT License - see [LICENSE](./LICENSE) for details.

## 🙏 Acknowledgments

- **Syrian Visual Identity**: Inspired by [Syrian.zone](https://syrian.zone) cultural palette
- **Typography**: Qomra font by [IwanType](https://iwantype.com)
- **Community**: Built with love for the Syrian developer community
- **Open Source**: Standing on the shoulders of giants in the React ecosystem

---

**Built with ❤️ for Syria**

*Syrian Identity UI - Celebrating Syrian culture through authentic, accessible, and beautiful user interfaces.*
