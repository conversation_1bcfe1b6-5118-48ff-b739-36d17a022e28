# RTL (Right-to-Left) Implementation Guide

## Overview

This guide explains the RTL text alignment principles and implementation details for the Syrian Identity UI Input components, specifically designed for optimal Arabic and other RTL language user experiences.

## RTL Text Alignment Principles

### 1. Natural vs Opposite Alignment

**Natural Alignment (Recommended)**
- Placeholder text aligns to the right, matching where users start typing
- Consistent with Arabic reading patterns
- Most intuitive for native Arabic speakers
- Default behavior for RTL content

**Opposite Alignment (Alternative)**
- Placeholder text aligns to the left, showing where text will end
- Helps users understand text flow direction
- Useful for mixed-language environments
- Can reduce confusion about typing direction

### 2. Auto-Detection Algorithm

The component uses Unicode character analysis to automatically detect text direction:

```typescript
const detectTextDirection = (text: string): 'rtl' | 'ltr' => {
  // Arabic, Hebrew, and other RTL Unicode ranges
  const rtlChars = /[\u0590-\u05FF\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const ltrChars = /[A-Za-z]/;
  
  const rtlCount = (text.match(rtlChars) || []).length;
  const ltrCount = (text.match(ltrChars) || []).length;
  
  // RTL takes precedence when equal or greater
  return (rtlCount > 0 && rtlCount >= ltrCount) ? 'rtl' : 'ltr';
};
```

**Unicode Ranges Covered:**
- `\u0590-\u05FF`: Hebrew
- `\u0600-\u06FF`: Arabic
- `\u0750-\u077F`: Arabic Supplement
- `\u08A0-\u08FF`: Arabic Extended-A
- `\uFB50-\uFDFF`: Arabic Presentation Forms-A
- `\uFE70-\uFEFF`: Arabic Presentation Forms-B

## Implementation Details

### Component Props

```typescript
interface InputProps {
  /**
   * Text direction control
   * 'auto' - automatically detects from placeholder/value
   * 'rtl' - forces right-to-left
   * 'ltr' - forces left-to-right
   */
  dir?: 'rtl' | 'ltr' | 'auto';

  /**
   * Placeholder alignment in RTL contexts
   * 'natural' - follows text direction (Arabic UX standard)
   * 'opposite' - starts opposite to text (shows typing destination)
   */
  placeholderAlign?: 'natural' | 'opposite';
}
```

### CSS Implementation

The component generates dynamic CSS for placeholder styling:

```css
/* Natural alignment (default for RTL) */
.input-class::placeholder {
  text-align: right;
  direction: rtl;
  unicode-bidi: embed;
}

/* Opposite alignment */
.input-class::placeholder {
  text-align: left;
  direction: ltr;
  unicode-bidi: embed;
}
```

**Browser Compatibility:**
- `::placeholder` - Modern browsers
- `::-webkit-input-placeholder` - WebKit browsers
- `::-moz-placeholder` - Firefox
- `:-ms-input-placeholder` - IE/Edge legacy
- `::-ms-input-placeholder` - Edge modern

### Font Selection

The component automatically selects appropriate fonts based on detected direction:

```typescript
fontFamily: actualDirection === 'rtl' 
  ? 'var(--sid-font-arabic)' 
  : 'var(--sid-font-universal)'
```

## Best Practices

### 1. When to Use Each Alignment

**Use Natural Alignment When:**
- Primary audience is Arabic speakers
- Content is predominantly RTL
- Following standard Arabic UI patterns
- Building culturally authentic experiences

**Use Opposite Alignment When:**
- Mixed language environments
- Users frequently switch between RTL/LTR
- Need to clarify typing direction
- Educational or learning applications

### 2. Auto-Detection Guidelines

**Recommended for:**
- Multi-language applications
- Dynamic content scenarios
- User-generated content
- International applications

**Avoid when:**
- Performance is critical (slight overhead)
- Direction is always known
- Simple single-language apps

### 3. Accessibility Considerations

- Always set `dir` attribute on input elements
- Use `unicode-bidi: embed` for proper text rendering
- Ensure labels and helper text match input direction
- Test with screen readers in RTL mode
- Provide clear visual cues for direction changes

## Usage Examples

### Basic RTL Input
```tsx
<Input
  label="الاسم الكامل"
  placeholder="أدخل اسمك الكامل"
  dir="rtl"
  variant="cultural"
/>
```

### Auto-Detection
```tsx
<Input
  label="Smart Input"
  placeholder="أدخل النص العربي"
  dir="auto"
  placeholderAlign="natural"
/>
```

### Mixed Content Form
```tsx
<form>
  <Input
    label="Arabic Name"
    placeholder="الاسم بالعربية"
    dir="auto"
  />
  <Input
    label="English Name"
    placeholder="Name in English"
    dir="auto"
  />
</form>
```

## Testing RTL Implementation

### Manual Testing Checklist

- [ ] Placeholder aligns correctly in RTL context
- [ ] Text input flows from right to left
- [ ] Labels and helper text align properly
- [ ] Icons position correctly (start/end respect RTL)
- [ ] Validation messages align with input direction
- [ ] Focus states work properly
- [ ] Auto-detection works for mixed content

### Browser Testing

Test across major browsers:
- Chrome/Chromium (WebKit)
- Firefox (Gecko)
- Safari (WebKit)
- Edge (Chromium)

### Screen Reader Testing

- NVDA (Windows)
- JAWS (Windows)
- VoiceOver (macOS/iOS)
- TalkBack (Android)

## Common Issues and Solutions

### Issue: Placeholder appears on wrong side
**Solution:** Check `placeholderAlign` prop and ensure CSS is being applied

### Issue: Auto-detection not working
**Solution:** Verify placeholder content contains RTL characters

### Issue: Mixed content direction confusion
**Solution:** Use explicit `dir` prop instead of auto-detection

### Issue: Icons not positioning correctly
**Solution:** Ensure using `insetInlineStart`/`insetInlineEnd` instead of left/right

## Performance Considerations

- Auto-detection adds minimal overhead (~1ms)
- CSS generation is memoized and cached
- Unicode regex is optimized for common ranges
- Consider explicit direction for high-frequency renders

## Future Enhancements

- Support for additional RTL languages (Urdu, Persian, etc.)
- Advanced mixed-content handling
- Integration with browser's built-in direction detection
- Performance optimizations for large forms
